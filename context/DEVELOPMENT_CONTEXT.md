# Mumzworld Rating & Review Service - Development Context Document

## Service Overview

The Rating & Review service is a Laravel 11-based microservice designed to handle product reviews for Mumzworld's e-commerce platform. It provides comprehensive review management capabilities including creation, moderation, translation, and statistics calculation.

### Core Functionality
- **Customer Reviews**: Allow customers to submit product ratings and reviews with media
- **Admin Moderation**: Enable admin team to moderate reviews (approve, reject, flag)
- **Multilingual Support**: Automatic translation between English and Arabic
- **Statistics**: Real-time rating summaries and distribution analytics
- **Media Management**: Support for images and videos with CDN integration

## Technical Architecture

### Technology Stack
- **Framework**: Laravel 11.31 with PHP 8.2+
- **Primary Database**: DynamoDB for scalable review storage
- **Supporting Database**: MySQL for Laravel framework needs (sessions, jobs, cache)
- **Cache & Queues**: Redis for caching and background job processing
- **Queue Management**: Laravel Horizon for monitoring and management
- **External Services**: Google Translate API, AWS S3, CloudFront CDN

### Database Schema

#### DynamoDB Tables

**ratings_and_reviews**
```
Primary Key: review_id (Hash)
Global Secondary Indexes:
- product_id-index: product_id (Hash)
- user_id-index: user_id (Hash)
- publication_status-index: publication_status (Hash)

Attributes:
- review_id (String, UUID)
- user_id (String)
- product_id (String)
- rating (Number, 1-5)
- original_language (String, en|ar)
- review_en (String)
- review_ar (String)
- country (String, 2-letter code)
- publication_status (String, pending|published|rejected)
- created_at (String, ISO8601)
- media (List, JSON array of media objects)
```

**ratings_and_review_statistics**
```
Primary Key: product_id (Hash)

Attributes:
- product_id (String)
- rating_count (Number)
- average_rating (Number, decimal)
- rating_distribution (Map, {1: count, 2: count, ...})
- percentage_distribution (Map, {1: %, 2: %, ...})
- last_calculated_at (String, ISO8601)
```

### Application Structure

#### Models
- **RatingAndReview**: Main review model with DynamoDB integration
- **RatingsAndReviewStatistics**: Pre-calculated statistics model
- **User**: Basic user model (minimal implementation)

#### Controllers
- **ReviewController**: Core review CRUD operations and admin functions
- **ProductReviewController**: Product-specific review queries and statistics
- **ReviewTranslationController**: Translation functionality

#### Services
- **MediaUploadService**: Handles file uploads to various storage backends
- **TranslationService**: Google Translate API integration
- **RatingsAndReviewsStatisticsService**: Statistics calculation and management
- **CloudFrontService**: CDN cache invalidation management

#### Background Jobs
- **UpdateProductStatisticsJob**: Recalculates product rating statistics
- **InvalidateCloudFrontCache**: Handles CDN cache invalidation

## API Endpoints

### Customer-Facing APIs
```
POST   /api/reviews                           # Create review with media
GET    /api/reviews/{id}/translate            # Get translated review
GET    /api/products/{id}/reviews             # Get product reviews
GET    /api/products/{id}/rating              # Get rating summary
POST   /api/products/ratings-summary          # Bulk rating summaries
```

### Admin APIs
```
GET    /api/reviews                           # Filter reviews by status
GET    /api/reviews/pending-check             # Check for pending reviews
GET    /api/reviews/counts-by-status-bk       # Review counts by status
DELETE /api/reviews/{id}                      # Delete review
PUT    /api/reviews/{id}/publication          # Update publication status
```

## Development Workflow

### Local Development Setup
1. **Prerequisites**: Docker, Docker Compose, Git
2. **Clone Repository**: `git clone <repository-url>`
3. **Install Dependencies**: `composer install`
4. **Environment Setup**: Copy `.env.example` to `.env` and configure
5. **Start Services**: `docker-compose up -d`
6. **Run Migrations**: `docker-compose exec app php artisan migrate:dynamodb`
7. **Seed Data**: `docker-compose exec app php artisan db:seed --class=RatingAndReviewSeeder`

### Development Services
- **Application**: http://localhost:7001
- **Horizon Dashboard**: http://localhost:7001/horizon
- **phpMyAdmin**: http://localhost:8080
- **Redis Commander**: http://localhost:8081
- **DynamoDB Admin**: http://localhost:8001

### Custom Artisan Commands

#### DynamoDB Management
```bash
# Run DynamoDB migrations
php artisan migrate:dynamodb

# Run specific migration
php artisan migrate:dynamodb --file=filename.php

# Fresh migrations (drops tables first)
php artisan migrate:dynamodb --fresh

# Create new migration
php artisan make:dynamodb-migration create_table_name

# Create new DynamoDB model
php artisan make:dynamodb-model ModelName
```

#### Statistics Management
```bash
# Backfill statistics for all products
php artisan statistics:backfill

# Backfill for specific products
php artisan statistics:backfill --product_id=id1 --product_id=id2

# Custom chunk size for memory optimization
php artisan statistics:backfill --chunk_size=50
```

#### Translation Management
```bash
# Translate published reviews
php artisan reviews:translate --status=published --limit=100

# Translate pending reviews
php artisan reviews:translate --status=pending

# Translate specific language combinations
php artisan reviews:translate --from=en --to=ar --limit=50
```

#### Cache Management
```bash
# Invalidate all CloudFront cache
php artisan cloudfront:invalidate --all

# Invalidate specific review
php artisan cloudfront:invalidate --review=review_id

# Invalidate product API responses
php artisan cloudfront:invalidate --product=product_id
```

## Configuration

### Environment Variables

#### Application
```env
APP_NAME="Mumzworld Ratings and Reviews Service"
APP_ENV=local
APP_KEY=base64:your-app-key-here
APP_DEBUG=true
APP_URL=http://localhost
```

#### Database
```env
# MySQL (for Laravel framework needs)
DB_CONNECTION=mysql
DB_HOST=mysql
DB_PORT=3306
DB_DATABASE=app_db
DB_USERNAME=app_user
DB_PASSWORD=userpassword

# DynamoDB
DYNAMODB_CONNECTION=local
DYNAMODB_LOCAL_ENDPOINT=http://dynamodb:8000
AWS_ACCESS_KEY_ID=dynamodblocal
AWS_SECRET_ACCESS_KEY=secret
AWS_DEFAULT_REGION=us-east-1
```

#### Cache & Queues
```env
REDIS_HOST=redis
REDIS_PASSWORD=null
REDIS_PORT=6379
QUEUE_CONNECTION=redis
CACHE_DRIVER=redis
SESSION_DRIVER=redis
```

#### External Services
```env
# Google Translate API
GOOGLE_TRANSLATE_API_KEY=your-google-translate-api-key

# AWS S3 for media storage
AWS_BUCKET=your-s3-bucket
AWS_URL=https://your-cloudfront-domain.cloudfront.net

# CloudFront Cache Invalidation
CLOUDFRONT_DISTRIBUTION_ID=your-distribution-id
```

### Storage Configuration

The service supports multiple storage backends:

1. **Local Storage** (default)
   - Files stored in `storage/app/private/reviews/`
   - Access controlled by application

2. **Public Storage**
   - Set `FILESYSTEM_DISK=public`
   - Files stored in `storage/app/public/reviews/`
   - Requires `php artisan storage:link`

3. **S3 Storage**
   - Set `FILESYSTEM_DISK=s3`
   - Files stored in Amazon S3
   - CloudFront URLs if `AWS_URL` is configured

## Development Guidelines

### Code Standards
- Follow Laravel conventions and PSR-12 coding standards
- Use form requests for validation
- Implement proper error handling with try-catch blocks
- Add comprehensive logging for debugging
- Use service classes for business logic
- Keep controllers thin and focused on HTTP concerns

### Database Patterns
- Use DynamoDB for primary data storage
- Leverage Global Secondary Indexes for efficient queries
- Implement cursor-based pagination for large datasets
- Pre-calculate statistics for performance
- Use background jobs for heavy operations

### Testing Strategy (To Be Implemented)
```
tests/
├── Unit/
│   ├── Models/          # Model behavior and validation
│   ├── Services/        # Business logic testing
│   └── Jobs/           # Background job testing
├── Feature/
│   ├── API/            # API endpoint testing
│   ├── Admin/          # Admin functionality testing
│   └── Customer/       # Customer-facing features
└── Integration/
    ├── DynamoDB/       # Database integration testing
    ├── Translation/    # External service testing
    └── Storage/        # File storage testing
```

### Performance Considerations
- Use pre-calculated statistics for fast API responses
- Implement efficient pagination with DynamoDB cursors
- Queue heavy operations (statistics calculation, translations)
- Cache frequently accessed data in Redis
- Optimize media delivery through CloudFront CDN
- Monitor memory usage in statistics calculations

### Security Best Practices
- Validate all input using form requests
- Sanitize file uploads and validate file types
- Implement rate limiting on API endpoints
- Use environment variables for sensitive configuration
- Add authentication and authorization (currently missing)
- Implement proper error handling without exposing internals

## Common Development Tasks

### Adding New API Endpoints
1. Create form request class for validation
2. Add route in `routes/api.php`
3. Implement controller method
4. Add business logic to appropriate service
5. Create/update API resource for response formatting
6. Update OpenAPI documentation
7. Write tests for the new endpoint

### Modifying Database Schema
1. Create new DynamoDB migration file
2. Update model attributes and casts
3. Add/modify Global Secondary Indexes if needed
4. Update factory for testing data
5. Run migration: `php artisan migrate:dynamodb`

### Adding New Background Jobs
1. Create job class: `php artisan make:job JobName`
2. Implement job logic in `handle()` method
3. Add job to appropriate queue
4. Configure retry logic and backoff strategy
5. Test job execution and failure scenarios

### Integrating External Services
1. Create service class in `app/Services/`
2. Add configuration to environment variables
3. Implement error handling and retry logic
4. Add service to dependency injection container
5. Write integration tests

## Troubleshooting

### Common Issues

**DynamoDB Connection Problems**
- Check if DynamoDB container is running: `docker-compose ps dynamodb`
- Verify endpoint configuration in `.env`
- Check DynamoDB logs: `docker-compose logs dynamodb`

**Queue Jobs Not Processing**
- Check Horizon status: `php artisan horizon:status`
- Verify Redis connection: `Redis::ping()` in tinker
- Restart Horizon: `docker-compose restart horizon`

**Media Upload Issues**
- Check storage permissions: `ls -la storage/app/`
- Create storage link: `php artisan storage:link`
- Verify disk space: `df -h`

**Translation Service Failures**
- Verify Google Translate API key configuration
- Check API quotas and billing
- Review translation service logs

### Performance Monitoring
- Monitor Horizon dashboard for queue performance
- Check Redis memory usage and hit rates
- Monitor DynamoDB read/write capacity units
- Track API response times and error rates

## Future Development Roadmap

### High Priority
- Implement comprehensive test suite
- Add authentication and authorization
- Enhance error handling and monitoring
- Optimize memory usage in statistics calculation

### Medium Priority
- Add review analytics and reporting
- Implement advanced search capabilities
- Add webhook support for external integrations
- Enhance security with rate limiting and input sanitization

### Low Priority
- Machine learning for sentiment analysis
- Real-time updates with WebSocket integration
- Advanced caching strategies
- Microservice communication patterns

This context document should serve as a comprehensive guide for developers working on the Rating & Review service, providing all necessary information for understanding, maintaining, and extending the codebase.