<?php

namespace Tests\Integration\DynamoDB;

use App\Models\RatingAndReview;
use Database\Factories\RatingAndReviewFactory;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class RatingAndReviewIntegrationTest extends TestCase
{
    use RefreshDatabase;

    protected RatingAndReviewFactory $factory;

    protected function setUp(): void
    {
        parent::setUp();
        $this->factory = new RatingAndReviewFactory();
    }

    public function test_can_create_and_save_review_to_dynamodb()
    {
        $reviewData = [
            'user_id' => 'user_123',
            'product_id' => 'product_456',
            'rating' => 5,
            'original_language' => 'en',
            'review_en' => 'Excellent product!',
            'country' => 'AE',
            'publication_status' => 'published',
        ];

        $review = new RatingAndReview($reviewData);
        $result = $review->save();

        $this->assertTrue($result);
        $this->assertNotNull($review->review_id);
        $this->assertNotNull($review->created_at);
    }

    public function test_can_retrieve_review_by_id()
    {
        $review = $this->factory->create([
            'user_id' => 'user_123',
            'product_id' => 'product_456',
            'rating' => 4,
            'publication_status' => 'published',
        ]);

        $retrievedReview = RatingAndReview::find($review->review_id);

        $this->assertNotNull($retrievedReview);
        $this->assertEquals($review->review_id, $retrievedReview->review_id);
        $this->assertEquals($review->user_id, $retrievedReview->user_id);
        $this->assertEquals($review->product_id, $retrievedReview->product_id);
        $this->assertEquals($review->rating, $retrievedReview->rating);
    }

    public function test_can_query_reviews_by_product_id_using_gsi()
    {
        $productId = 'product_123';
        
        // Create reviews for the target product
        $review1 = $this->factory->create(['product_id' => $productId]);
        $review2 = $this->factory->create(['product_id' => $productId]);
        
        // Create review for different product
        $review3 = $this->factory->create(['product_id' => 'product_456']);

        $reviews = RatingAndReview::where('product_id', $productId)
            ->usingIndex('product_id-index')
            ->get();

        $this->assertCount(2, $reviews);
        $reviewIds = $reviews->pluck('review_id')->toArray();
        $this->assertContains($review1->review_id, $reviewIds);
        $this->assertContains($review2->review_id, $reviewIds);
        $this->assertNotContains($review3->review_id, $reviewIds);
    }

    public function test_can_query_reviews_by_user_id_using_gsi()
    {
        $userId = 'user_123';
        
        // Create reviews for the target user
        $review1 = $this->factory->create(['user_id' => $userId]);
        $review2 = $this->factory->create(['user_id' => $userId]);
        
        // Create review for different user
        $review3 = $this->factory->create(['user_id' => 'user_456']);

        $reviews = RatingAndReview::where('user_id', $userId)
            ->usingIndex('user_id-index')
            ->get();

        $this->assertCount(2, $reviews);
        $reviewIds = $reviews->pluck('review_id')->toArray();
        $this->assertContains($review1->review_id, $reviewIds);
        $this->assertContains($review2->review_id, $reviewIds);
        $this->assertNotContains($review3->review_id, $reviewIds);
    }

    public function test_can_query_reviews_by_publication_status_using_gsi()
    {
        // Create reviews with different statuses
        $publishedReview1 = $this->factory->create(['publication_status' => 'published']);
        $publishedReview2 = $this->factory->create(['publication_status' => 'published']);
        $pendingReview = $this->factory->create(['publication_status' => 'pending']);

        $publishedReviews = RatingAndReview::where('publication_status', 'published')
            ->usingIndex('publication_status-index')
            ->get();

        $this->assertCount(2, $publishedReviews);
        $reviewIds = $publishedReviews->pluck('review_id')->toArray();
        $this->assertContains($publishedReview1->review_id, $reviewIds);
        $this->assertContains($publishedReview2->review_id, $reviewIds);
        $this->assertNotContains($pendingReview->review_id, $reviewIds);
    }

    public function test_can_update_review_attributes()
    {
        $review = $this->factory->create([
            'publication_status' => 'pending',
            'review_en' => 'Original review text',
        ]);

        // Update the review
        $review->publication_status = 'published';
        $review->review_en = 'Updated review text';
        $result = $review->save();

        $this->assertTrue($result);

        // Retrieve and verify the update
        $updatedReview = RatingAndReview::find($review->review_id);
        $this->assertEquals('published', $updatedReview->publication_status);
        $this->assertEquals('Updated review text', $updatedReview->review_en);
    }

    public function test_can_delete_review()
    {
        $review = $this->factory->create();
        $reviewId = $review->review_id;

        $result = $review->delete();
        $this->assertTrue($result);

        // Verify the review is deleted
        $deletedReview = RatingAndReview::find($reviewId);
        $this->assertNull($deletedReview);
    }

    public function test_media_attribute_serialization()
    {
        $mediaData = [
            [
                'id' => 'media-123',
                'type' => 'image',
                'path' => 'reviews/test/image.jpg',
                'url' => 'https://example.com/image.jpg',
            ],
            [
                'id' => 'media-456',
                'type' => 'video',
                'path' => 'reviews/test/video.mp4',
                'url' => 'https://example.com/video.mp4',
            ],
        ];

        $review = $this->factory->create(['media' => $mediaData]);

        // Retrieve and verify media serialization
        $retrievedReview = RatingAndReview::find($review->review_id);
        $this->assertIsArray($retrievedReview->media);
        $this->assertCount(2, $retrievedReview->media);
        $this->assertEquals($mediaData, $retrievedReview->media);
    }

    public function test_datetime_attribute_handling()
    {
        $review = $this->factory->create();

        // Retrieve and verify datetime handling
        $retrievedReview = RatingAndReview::find($review->review_id);
        $this->assertInstanceOf(\Carbon\Carbon::class, $retrievedReview->created_at);
        $this->assertNotNull($retrievedReview->created_at);
    }

    public function test_scopes_work_with_dynamodb()
    {
        // Create reviews with different attributes
        $publishedReview = $this->factory->create(['publication_status' => 'published']);
        $pendingReview = $this->factory->create(['publication_status' => 'pending']);
        $englishReview = $this->factory->create(['original_language' => 'en']);
        $arabicReview = $this->factory->create(['original_language' => 'ar']);
        $fiveStarReview = $this->factory->create(['rating' => 5]);
        $fourStarReview = $this->factory->create(['rating' => 4]);

        // Test published scope
        $publishedReviews = RatingAndReview::published()->get();
        $this->assertGreaterThanOrEqual(1, $publishedReviews->count());

        // Test language scope
        $englishReviews = RatingAndReview::inLanguage('en')->get();
        $this->assertGreaterThanOrEqual(1, $englishReviews->count());

        // Test rating scope
        $fiveStarReviews = RatingAndReview::withRating(5)->get();
        $this->assertGreaterThanOrEqual(1, $fiveStarReviews->count());
    }

    public function test_can_combine_multiple_query_conditions()
    {
        $productId = 'product_123';
        
        // Create reviews with different combinations
        $targetReview = $this->factory->create([
            'product_id' => $productId,
            'publication_status' => 'published',
            'rating' => 5,
            'country' => 'AE',
        ]);
        
        $this->factory->create([
            'product_id' => $productId,
            'publication_status' => 'pending', // Different status
            'rating' => 5,
            'country' => 'AE',
        ]);

        $reviews = RatingAndReview::where('product_id', $productId)
            ->usingIndex('product_id-index')
            ->where('publication_status', 'published')
            ->where('rating', 5)
            ->where('country', 'AE')
            ->get();

        $this->assertCount(1, $reviews);
        $this->assertEquals($targetReview->review_id, $reviews->first()->review_id);
    }

    public function test_pagination_with_dynamodb()
    {
        $productId = 'product_123';
        
        // Create multiple reviews
        for ($i = 0; $i < 15; $i++) {
            $this->factory->create(['product_id' => $productId]);
        }

        // Test pagination
        $firstPage = RatingAndReview::where('product_id', $productId)
            ->usingIndex('product_id-index')
            ->limit(10)
            ->get();

        $this->assertCount(10, $firstPage);
    }

    public function test_error_handling_for_invalid_review_id()
    {
        $review = RatingAndReview::find('nonexistent-review-id');
        $this->assertNull($review);
    }

    public function test_batch_operations()
    {
        // Create multiple reviews
        $reviews = [];
        for ($i = 0; $i < 5; $i++) {
            $reviews[] = $this->factory->create([
                'product_id' => 'product_batch_test',
                'rating' => $i + 1,
            ]);
        }

        // Verify all reviews were created
        $retrievedReviews = RatingAndReview::where('product_id', 'product_batch_test')
            ->usingIndex('product_id-index')
            ->get();

        $this->assertCount(5, $retrievedReviews);
    }
}
