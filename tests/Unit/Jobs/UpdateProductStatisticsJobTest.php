<?php

namespace Tests\Unit\Jobs;

use App\Jobs\UpdateProductStatisticsJob;
use App\Services\RatingsAndReviewsStatisticsService;
use Illuminate\Support\Facades\Log;
use PHPUnit\Framework\TestCase;
use Mockery;

class UpdateProductStatisticsJobTest extends TestCase
{
    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    public function test_job_can_be_instantiated()
    {
        $productId = 'test-product-123';
        $job = new UpdateProductStatisticsJob($productId);
        
        $this->assertInstanceOf(UpdateProductStatisticsJob::class, $job);
    }

    public function test_job_has_correct_properties()
    {
        $productId = 'test-product-123';
        $job = new UpdateProductStatisticsJob($productId);
        
        // Test that job has the expected properties
        $this->assertEquals(3, $job->tries);
        $this->assertEquals([30, 60, 120], $job->backoff);
        $this->assertEquals('statistics', $job->queue);
    }

    public function test_job_stores_product_id()
    {
        $productId = 'test-product-123';
        $job = new UpdateProductStatisticsJob($productId);
        
        // Use reflection to access protected property
        $reflection = new \ReflectionClass($job);
        $property = $reflection->getProperty('productId');
        $property->setAccessible(true);
        
        $this->assertEquals($productId, $property->getValue($job));
    }

    public function test_handle_method_calls_statistics_service()
    {
        $productId = 'test-product-123';
        $job = new UpdateProductStatisticsJob($productId);
        
        // Mock the statistics service
        $statisticsService = Mockery::mock(RatingsAndReviewsStatisticsService::class);
        $statisticsService->shouldReceive('calculate')
            ->once()
            ->with($productId)
            ->andReturn(true);
        
        // Mock logging
        Log::shouldReceive('info')
            ->twice() // Start and completion logs
            ->andReturn(true);
        
        // Execute the job
        $job->handle($statisticsService);
        
        // Assertions are handled by Mockery expectations
        $this->assertTrue(true);
    }

    public function test_handle_method_logs_start_message()
    {
        $productId = 'test-product-123';
        $job = new UpdateProductStatisticsJob($productId);
        
        // Mock the statistics service
        $statisticsService = Mockery::mock(RatingsAndReviewsStatisticsService::class);
        $statisticsService->shouldReceive('calculate')
            ->andReturn(true);
        
        // Mock logging - expect specific start message
        Log::shouldReceive('info')
            ->with("[Job] UpdateProductStatisticsJob started for product ID: {$productId}")
            ->once()
            ->andReturn(true);
        
        Log::shouldReceive('info')
            ->with("[Job] UpdateProductStatisticsJob completed successfully for product ID: {$productId}")
            ->once()
            ->andReturn(true);
        
        $job->handle($statisticsService);
        
        $this->assertTrue(true);
    }

    public function test_handle_method_logs_completion_message_on_success()
    {
        $productId = 'test-product-123';
        $job = new UpdateProductStatisticsJob($productId);
        
        // Mock the statistics service to return success
        $statisticsService = Mockery::mock(RatingsAndReviewsStatisticsService::class);
        $statisticsService->shouldReceive('calculate')
            ->with($productId)
            ->andReturn(true);
        
        // Mock logging
        Log::shouldReceive('info')
            ->with("[Job] UpdateProductStatisticsJob started for product ID: {$productId}")
            ->once();
        
        Log::shouldReceive('info')
            ->with("[Job] UpdateProductStatisticsJob completed successfully for product ID: {$productId}")
            ->once();
        
        $job->handle($statisticsService);
        
        $this->assertTrue(true);
    }

    public function test_handle_method_logs_warning_on_service_failure()
    {
        $productId = 'test-product-123';
        $job = new UpdateProductStatisticsJob($productId);
        
        // Mock the statistics service to return failure
        $statisticsService = Mockery::mock(RatingsAndReviewsStatisticsService::class);
        $statisticsService->shouldReceive('calculate')
            ->with($productId)
            ->andReturn(false);
        
        // Mock logging
        Log::shouldReceive('info')
            ->with("[Job] UpdateProductStatisticsJob started for product ID: {$productId}")
            ->once();
        
        Log::shouldReceive('warning')
            ->with("[Job] UpdateProductStatisticsJob: calculate method indicated an issue or no data for product ID: {$productId}")
            ->once();
        
        $job->handle($statisticsService);
        
        $this->assertTrue(true);
    }

    public function test_handle_method_rethrows_exceptions()
    {
        $productId = 'test-product-123';
        $job = new UpdateProductStatisticsJob($productId);
        
        // Mock the statistics service to throw an exception
        $exception = new \Exception('Test exception');
        $statisticsService = Mockery::mock(RatingsAndReviewsStatisticsService::class);
        $statisticsService->shouldReceive('calculate')
            ->with($productId)
            ->andThrow($exception);
        
        // Mock logging
        Log::shouldReceive('info')
            ->with("[Job] UpdateProductStatisticsJob started for product ID: {$productId}")
            ->once();
        
        Log::shouldReceive('error')
            ->with("[Job] UpdateProductStatisticsJob failed for product ID: {$productId}. Error: Test exception")
            ->once();
        
        // Expect the exception to be rethrown
        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('Test exception');
        
        $job->handle($statisticsService);
    }

    public function test_job_implements_should_queue_interface()
    {
        $job = new UpdateProductStatisticsJob('test-product');
        
        $this->assertInstanceOf(\Illuminate\Contracts\Queue\ShouldQueue::class, $job);
    }

    public function test_job_uses_correct_traits()
    {
        $reflection = new \ReflectionClass(UpdateProductStatisticsJob::class);
        $traits = $reflection->getTraitNames();
        
        $expectedTraits = [
            'Illuminate\Bus\Queueable',
            'Illuminate\Foundation\Bus\Dispatchable',
            'Illuminate\Queue\InteractsWithQueue',
            'Illuminate\Queue\SerializesModels',
        ];
        
        foreach ($expectedTraits as $trait) {
            $this->assertContains($trait, $traits);
        }
    }

    public function test_job_has_correct_queue_configuration()
    {
        $job = new UpdateProductStatisticsJob('test-product');
        
        // Test retry configuration
        $this->assertEquals(3, $job->tries);
        $this->assertEquals([30, 60, 120], $job->backoff);
        $this->assertEquals('statistics', $job->queue);
    }

    public function test_job_constructor_sets_queue()
    {
        $productId = 'test-product-123';
        $job = new UpdateProductStatisticsJob($productId);
        
        $this->assertEquals('statistics', $job->queue);
    }

    public function test_job_can_handle_empty_product_id()
    {
        $job = new UpdateProductStatisticsJob('');
        
        // Mock the statistics service
        $statisticsService = Mockery::mock(RatingsAndReviewsStatisticsService::class);
        $statisticsService->shouldReceive('calculate')
            ->with('')
            ->andReturn(false);
        
        // Mock logging
        Log::shouldReceive('info')
            ->with("[Job] UpdateProductStatisticsJob started for product ID: ")
            ->once();
        
        Log::shouldReceive('warning')
            ->with("[Job] UpdateProductStatisticsJob: calculate method indicated an issue or no data for product ID: ")
            ->once();
        
        $job->handle($statisticsService);
        
        $this->assertTrue(true);
    }

    public function test_job_can_handle_null_product_id()
    {
        $job = new UpdateProductStatisticsJob(null);
        
        // Mock the statistics service
        $statisticsService = Mockery::mock(RatingsAndReviewsStatisticsService::class);
        $statisticsService->shouldReceive('calculate')
            ->with(null)
            ->andReturn(false);
        
        // Mock logging
        Log::shouldReceive('info')
            ->with("[Job] UpdateProductStatisticsJob started for product ID: ")
            ->once();
        
        Log::shouldReceive('warning')
            ->with("[Job] UpdateProductStatisticsJob: calculate method indicated an issue or no data for product ID: ")
            ->once();
        
        $job->handle($statisticsService);
        
        $this->assertTrue(true);
    }

    public function test_job_serialization()
    {
        $productId = 'test-product-123';
        $job = new UpdateProductStatisticsJob($productId);
        
        // Test that the job can be serialized and unserialized
        $serialized = serialize($job);
        $unserialized = unserialize($serialized);
        
        $this->assertInstanceOf(UpdateProductStatisticsJob::class, $unserialized);
        
        // Check that the product ID is preserved
        $reflection = new \ReflectionClass($unserialized);
        $property = $reflection->getProperty('productId');
        $property->setAccessible(true);
        
        $this->assertEquals($productId, $property->getValue($unserialized));
    }
}
