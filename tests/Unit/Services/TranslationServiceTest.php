<?php

namespace Tests\Unit\Services;

use App\Models\RatingAndReview;
use App\Services\TranslationService;
use Illuminate\Http\Client\Response;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use PHPUnit\Framework\TestCase;
use Mockery;

class TranslationServiceTest extends TestCase
{
    protected TranslationService $service;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Mock config values
        Config::shouldReceive('get')
            ->with('services.google_translate.api_key')
            ->andReturn('test-api-key');
        Config::shouldReceive('get')
            ->with('services.google_translate.endpoint')
            ->andReturn('https://translation.googleapis.com/language/translate/v2');

        $this->service = new TranslationService();
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    public function test_service_initializes_with_config_values()
    {
        $this->assertInstanceOf(TranslationService::class, $this->service);
    }

    public function test_translate_review_with_english_original()
    {
        // Create a mock review
        $review = Mockery::mock(RatingAndReview::class);
        $review->shouldReceive('getAttribute')->with('review_id')->andReturn('test-review-id');
        $review->shouldReceive('getAttribute')->with('original_language')->andReturn('en');
        $review->shouldReceive('getAttribute')->with('review_en')->andReturn('Great product!');
        $review->shouldReceive('getAttribute')->with('review_ar')->andReturn(null);
        $review->shouldReceive('setAttribute')->with('review_ar', Mockery::any());
        $review->shouldReceive('save')->andReturn(true);

        // Mock HTTP response
        Http::shouldReceive('get')
            ->andReturn(new Response(Mockery::mock(\Psr\Http\Message\ResponseInterface::class, [
                'getStatusCode' => 200,
                'getBody' => json_encode([
                    'data' => [
                        'translations' => [
                            ['translatedText' => 'منتج رائع!']
                        ]
                    ]
                ])
            ])));

        Log::shouldReceive('debug')->andReturn(true);
        Log::shouldReceive('info')->andReturn(true);

        $result = $this->service->translateReview($review);
        $this->assertTrue($result);
    }

    public function test_translate_review_with_arabic_original()
    {
        // Create a mock review
        $review = Mockery::mock(RatingAndReview::class);
        $review->shouldReceive('getAttribute')->with('review_id')->andReturn('test-review-id');
        $review->shouldReceive('getAttribute')->with('original_language')->andReturn('ar');
        $review->shouldReceive('getAttribute')->with('review_ar')->andReturn('منتج رائع!');
        $review->shouldReceive('getAttribute')->with('review_en')->andReturn(null);
        $review->shouldReceive('setAttribute')->with('review_en', Mockery::any());
        $review->shouldReceive('save')->andReturn(true);

        // Mock HTTP response
        Http::shouldReceive('get')
            ->andReturn(new Response(Mockery::mock(\Psr\Http\Message\ResponseInterface::class, [
                'getStatusCode' => 200,
                'getBody' => json_encode([
                    'data' => [
                        'translations' => [
                            ['translatedText' => 'Great product!']
                        ]
                    ]
                ])
            ])));

        Log::shouldReceive('debug')->andReturn(true);
        Log::shouldReceive('info')->andReturn(true);

        $result = $this->service->translateReview($review);
        $this->assertTrue($result);
    }

    public function test_translate_review_skips_when_translation_exists()
    {
        // Create a mock review with both translations
        $review = Mockery::mock(RatingAndReview::class);
        $review->shouldReceive('getAttribute')->with('review_id')->andReturn('test-review-id');
        $review->shouldReceive('getAttribute')->with('original_language')->andReturn('en');
        $review->shouldReceive('getAttribute')->with('review_en')->andReturn('Great product!');
        $review->shouldReceive('getAttribute')->with('review_ar')->andReturn('منتج رائع!');

        Log::shouldReceive('info')->andReturn(true);

        $result = $this->service->translateReview($review);
        $this->assertFalse($result);
    }

    public function test_translate_review_skips_when_source_content_empty()
    {
        // Create a mock review with empty source content
        $review = Mockery::mock(RatingAndReview::class);
        $review->shouldReceive('getAttribute')->with('review_id')->andReturn('test-review-id');
        $review->shouldReceive('getAttribute')->with('original_language')->andReturn('en');
        $review->shouldReceive('getAttribute')->with('review_en')->andReturn('');
        $review->shouldReceive('getAttribute')->with('review_ar')->andReturn(null);

        Log::shouldReceive('warning')->andReturn(true);

        $result = $this->service->translateReview($review);
        $this->assertFalse($result);
    }

    public function test_translate_method_success()
    {
        // Mock successful HTTP response
        $mockResponse = Mockery::mock(Response::class);
        $mockResponse->shouldReceive('successful')->andReturn(true);
        $mockResponse->shouldReceive('json')->andReturn([
            'data' => [
                'translations' => [
                    ['translatedText' => 'Translated text']
                ]
            ]
        ]);

        Http::shouldReceive('get')->andReturn($mockResponse);

        $reflection = new \ReflectionClass(TranslationService::class);
        $method = $reflection->getMethod('translate');
        $method->setAccessible(true);

        $result = $method->invokeArgs($this->service, ['Hello world', 'en', 'ar']);
        $this->assertEquals('Translated text', $result);
    }

    public function test_translate_method_handles_api_failure()
    {
        // Mock failed HTTP response
        $mockResponse = Mockery::mock(Response::class);
        $mockResponse->shouldReceive('successful')->andReturn(false);
        $mockResponse->shouldReceive('status')->andReturn(400);
        $mockResponse->shouldReceive('body')->andReturn('Bad Request');

        Http::shouldReceive('get')->andReturn($mockResponse);
        Log::shouldReceive('error')->andReturn(true);

        $reflection = new \ReflectionClass(TranslationService::class);
        $method = $reflection->getMethod('translate');
        $method->setAccessible(true);

        $this->expectException(\Exception::class);
        $method->invokeArgs($this->service, ['Hello world', 'en', 'ar']);
    }

    public function test_translate_method_handles_empty_response()
    {
        // Mock response with empty translation
        $mockResponse = Mockery::mock(Response::class);
        $mockResponse->shouldReceive('successful')->andReturn(true);
        $mockResponse->shouldReceive('json')->andReturn([
            'data' => [
                'translations' => [
                    ['translatedText' => '']
                ]
            ]
        ]);

        Http::shouldReceive('get')->andReturn($mockResponse);
        Log::shouldReceive('warning')->andReturn(true);

        $reflection = new \ReflectionClass(TranslationService::class);
        $method = $reflection->getMethod('translate');
        $method->setAccessible(true);

        $result = $method->invokeArgs($this->service, ['Hello world', 'en', 'ar']);
        $this->assertEquals('', $result);
    }

    public function test_translate_method_throws_exception_when_api_key_missing()
    {
        // Create service with missing API key
        Config::shouldReceive('get')
            ->with('services.google_translate.api_key')
            ->andReturn('');

        $service = new TranslationService();

        $reflection = new \ReflectionClass(TranslationService::class);
        $method = $reflection->getMethod('translate');
        $method->setAccessible(true);

        Log::shouldReceive('error')->andReturn(true);

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('Google Translate API key is not configured');
        
        $method->invokeArgs($service, ['Hello world', 'en', 'ar']);
    }

    public function test_translate_method_throws_exception_when_endpoint_missing()
    {
        // Create service with missing endpoint
        Config::shouldReceive('get')
            ->with('services.google_translate.api_key')
            ->andReturn('test-key');
        Config::shouldReceive('get')
            ->with('services.google_translate.endpoint')
            ->andReturn('');

        $service = new TranslationService();

        $reflection = new \ReflectionClass(TranslationService::class);
        $method = $reflection->getMethod('translate');
        $method->setAccessible(true);

        Log::shouldReceive('error')->andReturn(true);

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('Google Translate API endpoint is not configured');
        
        $method->invokeArgs($service, ['Hello world', 'en', 'ar']);
    }

    public function test_batch_translate_reviews_with_empty_collection()
    {
        $emptyCollection = new Collection([]);
        
        Log::shouldReceive('info')->with(Mockery::pattern('/empty collection/'));

        $this->service->batchTranslateReviews($emptyCollection);
        
        // Should complete without errors
        $this->assertTrue(true);
    }

    public function test_batch_translate_reviews_with_multiple_reviews()
    {
        // Create mock reviews
        $review1 = Mockery::mock(RatingAndReview::class);
        $review1->shouldReceive('getAttribute')->with('review_id')->andReturn('review-1');
        $review1->shouldReceive('getAttribute')->with('original_language')->andReturn('en');
        $review1->shouldReceive('getAttribute')->with('review_en')->andReturn('Great!');
        $review1->shouldReceive('getAttribute')->with('review_ar')->andReturn(null);
        $review1->shouldReceive('setAttribute')->with('review_ar', Mockery::any());
        $review1->shouldReceive('save')->andReturn(true);

        $review2 = Mockery::mock(RatingAndReview::class);
        $review2->shouldReceive('getAttribute')->with('review_id')->andReturn('review-2');
        $review2->shouldReceive('getAttribute')->with('original_language')->andReturn('ar');
        $review2->shouldReceive('getAttribute')->with('review_ar')->andReturn('رائع!');
        $review2->shouldReceive('getAttribute')->with('review_en')->andReturn(null);
        $review2->shouldReceive('setAttribute')->with('review_en', Mockery::any());
        $review2->shouldReceive('save')->andReturn(true);

        $collection = new Collection([$review1, $review2]);

        // Mock HTTP responses
        Http::shouldReceive('get')
            ->andReturn(new Response(Mockery::mock(\Psr\Http\Message\ResponseInterface::class, [
                'getStatusCode' => 200,
                'getBody' => json_encode([
                    'data' => [
                        'translations' => [
                            ['translatedText' => 'Translated text']
                        ]
                    ]
                ])
            ])));

        Log::shouldReceive('info')->andReturn(true);
        Log::shouldReceive('debug')->andReturn(true);

        $this->service->batchTranslateReviews($collection);
        
        // Should complete without errors
        $this->assertTrue(true);
    }

    public function test_translate_review_handles_save_failure()
    {
        // Create a mock review
        $review = Mockery::mock(RatingAndReview::class);
        $review->shouldReceive('getAttribute')->with('review_id')->andReturn('test-review-id');
        $review->shouldReceive('getAttribute')->with('original_language')->andReturn('en');
        $review->shouldReceive('getAttribute')->with('review_en')->andReturn('Great product!');
        $review->shouldReceive('getAttribute')->with('review_ar')->andReturn(null);
        $review->shouldReceive('setAttribute')->with('review_ar', Mockery::any());
        $review->shouldReceive('save')->andReturn(false); // Simulate save failure

        // Mock HTTP response
        Http::shouldReceive('get')
            ->andReturn(new Response(Mockery::mock(\Psr\Http\Message\ResponseInterface::class, [
                'getStatusCode' => 200,
                'getBody' => json_encode([
                    'data' => [
                        'translations' => [
                            ['translatedText' => 'منتج رائع!']
                        ]
                    ]
                ])
            ])));

        Log::shouldReceive('debug')->andReturn(true);
        Log::shouldReceive('error')->andReturn(true);

        $result = $this->service->translateReview($review);
        $this->assertFalse($result);
    }
}
