<?php

namespace Tests\Unit\Services;

use App\Jobs\InvalidateCloudFrontCache;
use App\Services\CloudFrontService;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Queue;
use PHPUnit\Framework\TestCase;

class CloudFrontServiceTest extends TestCase
{
    protected CloudFrontService $service;

    protected function setUp(): void
    {
        parent::setUp();
        $this->service = new CloudFrontService();
    }

    public function test_service_can_be_instantiated()
    {
        $this->assertInstanceOf(CloudFrontService::class, $this->service);
    }

    public function test_service_has_correct_queue_name()
    {
        $reflection = new \ReflectionClass(CloudFrontService::class);
        $constant = $reflection->getConstant('INVALIDATION_QUEUE');
        
        $this->assertEquals('cache-invalidation', $constant);
    }

    public function test_invalidate_review_media_method_exists()
    {
        $this->assertTrue(method_exists($this->service, 'invalidateReviewMedia'));
    }

    public function test_invalidate_all_review_media_method_exists()
    {
        $this->assertTrue(method_exists($this->service, 'invalidateAllReviewMedia'));
    }

    public function test_invalidate_product_reviews_api_method_exists()
    {
        $this->assertTrue(method_exists($this->service, 'invalidateProductReviewsApi'));
    }

    public function test_invalidate_review_api_method_exists()
    {
        $this->assertTrue(method_exists($this->service, 'invalidateReviewApi'));
    }

    public function test_invalidate_all_reviews_api_method_exists()
    {
        $this->assertTrue(method_exists($this->service, 'invalidateAllReviewsApi'));
    }

    public function test_invalidate_paths_method_exists()
    {
        $this->assertTrue(method_exists($this->service, 'invalidatePaths'));
    }

    public function test_service_methods_are_public()
    {
        $reflection = new \ReflectionClass(CloudFrontService::class);
        
        $publicMethods = [
            'invalidateReviewMedia',
            'invalidateAllReviewMedia',
            'invalidateProductReviewsApi',
            'invalidateReviewApi',
            'invalidateAllReviewsApi',
            'invalidatePaths',
        ];

        foreach ($publicMethods as $methodName) {
            $method = $reflection->getMethod($methodName);
            $this->assertTrue($method->isPublic(), "Method {$methodName} should be public");
        }
    }

    public function test_invalidate_review_media_accepts_string_parameter()
    {
        $reflection = new \ReflectionClass(CloudFrontService::class);
        $method = $reflection->getMethod('invalidateReviewMedia');
        
        $parameters = $method->getParameters();
        $this->assertCount(1, $parameters);
        $this->assertEquals('reviewId', $parameters[0]->getName());
        $this->assertEquals('string', $parameters[0]->getType()->getName());
    }

    public function test_invalidate_product_reviews_api_accepts_string_parameter()
    {
        $reflection = new \ReflectionClass(CloudFrontService::class);
        $method = $reflection->getMethod('invalidateProductReviewsApi');
        
        $parameters = $method->getParameters();
        $this->assertCount(1, $parameters);
        $this->assertEquals('productId', $parameters[0]->getName());
        $this->assertEquals('string', $parameters[0]->getType()->getName());
    }

    public function test_invalidate_review_api_accepts_string_parameter()
    {
        $reflection = new \ReflectionClass(CloudFrontService::class);
        $method = $reflection->getMethod('invalidateReviewApi');
        
        $parameters = $method->getParameters();
        $this->assertCount(1, $parameters);
        $this->assertEquals('reviewId', $parameters[0]->getName());
        $this->assertEquals('string', $parameters[0]->getType()->getName());
    }

    public function test_invalidate_paths_accepts_correct_parameters()
    {
        $reflection = new \ReflectionClass(CloudFrontService::class);
        $method = $reflection->getMethod('invalidatePaths');
        
        $parameters = $method->getParameters();
        $this->assertCount(2, $parameters);
        $this->assertEquals('paths', $parameters[0]->getName());
        $this->assertEquals('async', $parameters[1]->getName());
        $this->assertTrue($parameters[1]->hasType());
        $this->assertEquals('bool', $parameters[1]->getType()->getName());
        $this->assertTrue($parameters[1]->isDefaultValueAvailable());
        $this->assertTrue($parameters[1]->getDefaultValue());
    }

    public function test_service_class_structure()
    {
        $reflection = new \ReflectionClass(CloudFrontService::class);
        
        // Test that it's a concrete class
        $this->assertFalse($reflection->isAbstract());
        $this->assertFalse($reflection->isInterface());
        $this->assertFalse($reflection->isTrait());
        
        // Test namespace
        $this->assertEquals('App\Services', $reflection->getNamespaceName());
    }

    public function test_service_has_expected_constants()
    {
        $reflection = new \ReflectionClass(CloudFrontService::class);
        $constants = $reflection->getConstants();
        
        $this->assertArrayHasKey('INVALIDATION_QUEUE', $constants);
        $this->assertEquals('cache-invalidation', $constants['INVALIDATION_QUEUE']);
    }

    public function test_method_return_types()
    {
        $reflection = new \ReflectionClass(CloudFrontService::class);
        
        $voidMethods = [
            'invalidateReviewMedia',
            'invalidateAllReviewMedia',
            'invalidateProductReviewsApi',
            'invalidateReviewApi',
            'invalidateAllReviewsApi',
            'invalidatePaths',
        ];

        foreach ($voidMethods as $methodName) {
            $method = $reflection->getMethod($methodName);
            $returnType = $method->getReturnType();
            $this->assertNotNull($returnType, "Method {$methodName} should have a return type");
            $this->assertEquals('void', $returnType->getName(), "Method {$methodName} should return void");
        }
    }

    public function test_service_constructor_has_no_parameters()
    {
        $reflection = new \ReflectionClass(CloudFrontService::class);
        $constructor = $reflection->getConstructor();

        if ($constructor !== null) {
            $parameters = $constructor->getParameters();
            $this->assertCount(0, $parameters, 'CloudFrontService constructor should have no parameters');
        } else {
            // If no constructor is defined, that's also valid
            $this->assertTrue(true, 'No constructor defined, which is acceptable');
        }
    }

    public function test_service_can_be_serialized()
    {
        $serialized = serialize($this->service);
        $unserialized = unserialize($serialized);
        
        $this->assertInstanceOf(CloudFrontService::class, $unserialized);
    }

    public function test_service_methods_exist_and_callable()
    {
        $methods = [
            'invalidateReviewMedia',
            'invalidateAllReviewMedia',
            'invalidateProductReviewsApi',
            'invalidateReviewApi',
            'invalidateAllReviewsApi',
            'invalidatePaths',
        ];

        foreach ($methods as $method) {
            $this->assertTrue(
                is_callable([$this->service, $method]),
                "Method {$method} should be callable"
            );
        }
    }

    public function test_service_has_no_static_methods()
    {
        $reflection = new \ReflectionClass(CloudFrontService::class);
        $methods = $reflection->getMethods(\ReflectionMethod::IS_STATIC);
        
        // Filter out any inherited static methods
        $ownStaticMethods = array_filter($methods, function ($method) {
            return $method->getDeclaringClass()->getName() === CloudFrontService::class;
        });
        
        $this->assertEmpty($ownStaticMethods, 'CloudFrontService should not have static methods');
    }

    public function test_service_implements_expected_interface_pattern()
    {
        // Test that the service follows expected patterns
        $reflection = new \ReflectionClass(CloudFrontService::class);

        // Should be in the Services namespace
        $this->assertStringContainsString('Services', $reflection->getName());

        // Should end with 'Service'
        $this->assertStringEndsWith('Service', $reflection->getShortName());
    }
}
