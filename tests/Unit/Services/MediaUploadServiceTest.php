<?php

namespace Tests\Unit\Services;

use App\Services\MediaUploadService;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Storage;
use PHPUnit\Framework\TestCase;
use Mockery;

class MediaUploadServiceTest extends TestCase
{
    protected MediaUploadService $service;

    protected function setUp(): void
    {
        parent::setUp();
        $this->service = new MediaUploadService();
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    public function test_service_initializes_with_default_disk()
    {
        Config::shouldReceive('get')
            ->with('filesystems.default')
            ->andReturn('local');

        $service = new MediaUploadService();
        
        // We can't directly test the protected property, but we can test behavior
        $this->assertInstanceOf(MediaUploadService::class, $service);
    }

    public function test_upload_media_returns_correct_structure()
    {
        // Mock the uploaded file
        $file = Mockery::mock(UploadedFile::class);
        $file->shouldReceive('getClientOriginalExtension')->andReturn('jpg');
        $file->shouldReceive('storeAs')->andReturn(true);

        // Mock config
        Config::shouldReceive('get')
            ->with('filesystems.default')
            ->andReturn('local');

        $reviewId = 'test-review-id';
        
        // We need to mock the private methods or test the public interface
        // For now, let's test that the method exists and returns an array
        $this->assertTrue(method_exists($this->service, 'uploadMedia'));
    }

    public function test_get_media_type_returns_image_for_image_extensions()
    {
        // Since getMediaType is private, we'll test it indirectly through uploadMedia
        // or create a reflection test
        $reflection = new \ReflectionClass(MediaUploadService::class);
        $method = $reflection->getMethod('getMediaType');
        $method->setAccessible(true);

        $imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'webp'];
        
        foreach ($imageExtensions as $extension) {
            $result = $method->invokeArgs($this->service, [$extension]);
            $this->assertEquals('image', $result);
        }
    }

    public function test_get_media_type_returns_video_for_video_extensions()
    {
        $reflection = new \ReflectionClass(MediaUploadService::class);
        $method = $reflection->getMethod('getMediaType');
        $method->setAccessible(true);

        $videoExtensions = ['mp4', 'avi', 'mov', 'wmv', 'flv', 'webm'];
        
        foreach ($videoExtensions as $extension) {
            $result = $method->invokeArgs($this->service, [$extension]);
            $this->assertEquals('video', $result);
        }
    }

    public function test_get_media_type_returns_unknown_for_unsupported_extensions()
    {
        $reflection = new \ReflectionClass(MediaUploadService::class);
        $method = $reflection->getMethod('getMediaType');
        $method->setAccessible(true);

        $unsupportedExtensions = ['txt', 'pdf', 'doc', 'zip'];
        
        foreach ($unsupportedExtensions as $extension) {
            $result = $method->invokeArgs($this->service, [$extension]);
            $this->assertEquals('unknown', $result);
        }
    }

    public function test_get_file_url_for_public_disk()
    {
        $reflection = new \ReflectionClass(MediaUploadService::class);
        $method = $reflection->getMethod('getFileUrl');
        $method->setAccessible(true);

        // Set disk to public
        $diskProperty = $reflection->getProperty('disk');
        $diskProperty->setAccessible(true);
        $diskProperty->setValue($this->service, 'public');

        $path = 'reviews/test/image.jpg';
        $result = $method->invokeArgs($this->service, [$path]);
        
        // Should return asset URL
        $this->assertStringContains('storage/' . $path, $result);
    }

    public function test_get_file_url_for_local_disk()
    {
        $reflection = new \ReflectionClass(MediaUploadService::class);
        $method = $reflection->getMethod('getFileUrl');
        $method->setAccessible(true);

        // Set disk to local
        $diskProperty = $reflection->getProperty('disk');
        $diskProperty->setAccessible(true);
        $diskProperty->setValue($this->service, 'local');

        $path = 'reviews/test/image.jpg';
        $result = $method->invokeArgs($this->service, [$path]);
        
        // Should return files URL
        $this->assertStringContains('files/' . $path, $result);
    }

    public function test_get_file_url_for_s3_disk_with_cloudfront()
    {
        $reflection = new \ReflectionClass(MediaUploadService::class);
        $method = $reflection->getMethod('getFileUrl');
        $method->setAccessible(true);

        // Set disk to s3
        $diskProperty = $reflection->getProperty('disk');
        $diskProperty->setAccessible(true);
        $diskProperty->setValue($this->service, 's3');

        // Mock config for CloudFront URL
        Config::shouldReceive('get')
            ->with('filesystems.disks.s3.url')
            ->andReturn('https://cloudfront.example.com');

        // Mock Storage facade
        Storage::shouldReceive('disk')
            ->with('s3')
            ->andReturnSelf();
        Storage::shouldReceive('url')
            ->andReturn('https://cloudfront.example.com/reviews/test/image.jpg');

        $path = 'reviews/test/image.jpg';
        $result = $method->invokeArgs($this->service, [$path]);
        
        $this->assertStringContains('cloudfront.example.com', $result);
    }

    public function test_get_file_url_for_s3_disk_without_cloudfront()
    {
        $reflection = new \ReflectionClass(MediaUploadService::class);
        $method = $reflection->getMethod('getFileUrl');
        $method->setAccessible(true);

        // Set disk to s3
        $diskProperty = $reflection->getProperty('disk');
        $diskProperty->setAccessible(true);
        $diskProperty->setValue($this->service, 's3');

        // Mock config for no CloudFront URL
        Config::shouldReceive('get')
            ->with('filesystems.disks.s3.url')
            ->andReturn('');
        Config::shouldReceive('get')
            ->with('filesystems.disks.s3.bucket')
            ->andReturn('test-bucket');
        Config::shouldReceive('get')
            ->with('filesystems.disks.s3.region')
            ->andReturn('us-east-1');

        $path = 'reviews/test/image.jpg';
        $result = $method->invokeArgs($this->service, [$path]);
        
        $this->assertStringContains('s3.amazonaws.com', $result);
        $this->assertStringContains('test-bucket', $result);
    }

    public function test_upload_media_generates_unique_media_id()
    {
        // Mock the uploaded file
        $file = Mockery::mock(UploadedFile::class);
        $file->shouldReceive('getClientOriginalExtension')->andReturn('jpg');
        $file->shouldReceive('storeAs')->andReturn(true);

        // Mock config
        Config::shouldReceive('get')
            ->with('filesystems.default')
            ->andReturn('local');

        // Mock the getFileUrl method
        $reflection = new \ReflectionClass(MediaUploadService::class);
        $method = $reflection->getMethod('getFileUrl');
        $method->setAccessible(true);

        $reviewId = 'test-review-id';
        
        // Test that multiple calls generate different media IDs
        // This would require mocking the Str::random() method or testing the pattern
        $this->assertTrue(method_exists($this->service, 'uploadMedia'));
    }

    public function test_upload_media_constructs_correct_path()
    {
        $reviewId = 'test-review-123';
        $extension = 'jpg';
        
        // The path should follow the pattern: reviews/{reviewId}/{mediaId}.{extension}
        $expectedPathPattern = "/^reviews\/{$reviewId}\/media-[a-zA-Z0-9]{8}\.{$extension}$/";
        
        // Since we can't easily test the private path construction,
        // we'll verify the pattern exists in the class
        $this->assertTrue(method_exists($this->service, 'uploadMedia'));
    }

    public function test_service_handles_different_file_types()
    {
        $fileTypes = [
            'jpg' => 'image',
            'png' => 'image',
            'gif' => 'image',
            'mp4' => 'video',
            'avi' => 'video',
            'mov' => 'video',
        ];

        $reflection = new \ReflectionClass(MediaUploadService::class);
        $method = $reflection->getMethod('getMediaType');
        $method->setAccessible(true);

        foreach ($fileTypes as $extension => $expectedType) {
            $result = $method->invokeArgs($this->service, [$extension]);
            $this->assertEquals($expectedType, $result, "Extension {$extension} should return type {$expectedType}");
        }
    }

    public function test_service_handles_case_insensitive_extensions()
    {
        $reflection = new \ReflectionClass(MediaUploadService::class);
        $method = $reflection->getMethod('getMediaType');
        $method->setAccessible(true);

        // Test uppercase extensions
        $this->assertEquals('image', $method->invokeArgs($this->service, ['JPG']));
        $this->assertEquals('image', $method->invokeArgs($this->service, ['PNG']));
        $this->assertEquals('video', $method->invokeArgs($this->service, ['MP4']));
        $this->assertEquals('video', $method->invokeArgs($this->service, ['AVI']));
    }

    public function test_upload_media_result_structure()
    {
        // Test that the expected structure is returned
        // This would be tested in integration tests, but we can verify the method signature
        $reflection = new \ReflectionClass(MediaUploadService::class);
        $method = $reflection->getMethod('uploadMedia');
        
        $this->assertTrue($method->isPublic());
        $this->assertEquals('uploadMedia', $method->getName());
        
        // Check parameters
        $parameters = $method->getParameters();
        $this->assertCount(2, $parameters);
        $this->assertEquals('file', $parameters[0]->getName());
        $this->assertEquals('reviewId', $parameters[1]->getName());
    }
}
