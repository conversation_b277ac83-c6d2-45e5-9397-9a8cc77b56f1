<?php

namespace Tests\Unit\Models;

use App\Models\RatingAndReview;
use Illuminate\Support\Str;
use PHPUnit\Framework\TestCase;

class RatingAndReviewTest extends TestCase
{
    public function test_model_has_correct_table_name()
    {
        $model = new RatingAndReview();
        $this->assertEquals('ratings_and_reviews', $model->getTable());
    }

    public function test_model_has_correct_primary_key()
    {
        $model = new RatingAndReview();
        $this->assertEquals('review_id', $model->getKeyName());
        $this->assertEquals('string', $model->getKeyType());
        $this->assertFalse($model->getIncrementing());
    }

    public function test_model_has_correct_fillable_attributes()
    {
        $model = new RatingAndReview();
        $expectedFillable = [
            'review_id',
            'user_id',
            'product_id',
            'rating',
            'original_language',
            'review_en',
            'review_ar',
            'country',
            'created_at',
            'media',
            'publication_status',
        ];
        
        $this->assertEquals($expectedFillable, $model->getFillable());
    }

    public function test_model_has_correct_casts()
    {
        $model = new RatingAndReview();
        $expectedCasts = [
            'rating' => 'integer',
            'media' => 'array',
            'created_at' => 'datetime',
        ];
        
        foreach ($expectedCasts as $attribute => $cast) {
            $this->assertEquals($cast, $model->getCasts()[$attribute]);
        }
    }

    public function test_constructor_sets_default_values()
    {
        $model = new RatingAndReview();
        
        // Check that default values are set
        $this->assertNotNull($model->review_id);
        $this->assertTrue(Str::isUuid($model->review_id));
        $this->assertNotNull($model->created_at);
        $this->assertEquals('pending', $model->publication_status);
        $this->assertEquals([], $model->media);
    }

    public function test_constructor_respects_provided_attributes()
    {
        $attributes = [
            'review_id' => 'custom-review-id',
            'user_id' => 'user-123',
            'product_id' => 'product-456',
            'rating' => 5,
            'original_language' => 'en',
            'review_en' => 'Great product!',
            'country' => 'AE',
            'publication_status' => 'published',
        ];
        
        $model = new RatingAndReview($attributes);
        
        $this->assertEquals('custom-review-id', $model->review_id);
        $this->assertEquals('user-123', $model->user_id);
        $this->assertEquals('product-456', $model->product_id);
        $this->assertEquals(5, $model->rating);
        $this->assertEquals('en', $model->original_language);
        $this->assertEquals('Great product!', $model->review_en);
        $this->assertEquals('AE', $model->country);
        $this->assertEquals('published', $model->publication_status);
    }

    public function test_media_mutator_handles_array_input()
    {
        $model = new RatingAndReview();
        $mediaArray = [
            ['id' => 'media-1', 'type' => 'image', 'url' => 'http://example.com/image.jpg'],
            ['id' => 'media-2', 'type' => 'video', 'url' => 'http://example.com/video.mp4'],
        ];
        
        $model->media = $mediaArray;
        
        // The mutator should convert array to JSON string
        $this->assertEquals(json_encode($mediaArray), $model->getAttributes()['media']);
    }

    public function test_media_mutator_handles_string_input()
    {
        $model = new RatingAndReview();
        $mediaJson = '{"id":"media-1","type":"image"}';
        
        $model->media = $mediaJson;
        
        $this->assertEquals($mediaJson, $model->getAttributes()['media']);
    }

    public function test_media_mutator_handles_null_input()
    {
        $model = new RatingAndReview();
        
        $model->media = null;
        
        $this->assertEquals('[]', $model->getAttributes()['media']);
    }

    public function test_media_accessor_returns_array()
    {
        $model = new RatingAndReview();
        $mediaArray = [
            ['id' => 'media-1', 'type' => 'image'],
        ];
        
        $model->setRawAttributes(['media' => json_encode($mediaArray)]);
        
        // The accessor (via cast) should return array
        $this->assertEquals($mediaArray, $model->media);
    }

    public function test_published_scope()
    {
        // This test would require a mock query builder or database connection
        // For now, we'll test that the scope method exists
        $model = new RatingAndReview();
        $this->assertTrue(method_exists($model, 'scopePublished'));
    }

    public function test_pending_scope()
    {
        $model = new RatingAndReview();
        $this->assertTrue(method_exists($model, 'scopePending'));
    }

    public function test_rejected_scope()
    {
        $model = new RatingAndReview();
        $this->assertTrue(method_exists($model, 'scopeRejected'));
    }

    public function test_for_product_scope()
    {
        $model = new RatingAndReview();
        $this->assertTrue(method_exists($model, 'scopeForProduct'));
    }

    public function test_for_user_scope()
    {
        $model = new RatingAndReview();
        $this->assertTrue(method_exists($model, 'scopeForUser'));
    }

    public function test_in_language_scope()
    {
        $model = new RatingAndReview();
        $this->assertTrue(method_exists($model, 'scopeInLanguage'));
    }

    public function test_with_rating_scope()
    {
        $model = new RatingAndReview();
        $this->assertTrue(method_exists($model, 'scopeWithRating'));
    }

    public function test_from_country_scope()
    {
        $model = new RatingAndReview();
        $this->assertTrue(method_exists($model, 'scopeFromCountry'));
    }

    public function test_model_timestamps_disabled()
    {
        $model = new RatingAndReview();
        $this->assertFalse($model->timestamps);
    }

    public function test_model_connection_is_dynamodb()
    {
        $model = new RatingAndReview();
        $this->assertEquals('dynamodb', $model->getConnectionName());
    }

    public function test_rating_validation_range()
    {
        // Test that rating is properly cast to integer
        $model = new RatingAndReview(['rating' => '5']);
        $this->assertIsInt($model->rating);
        $this->assertEquals(5, $model->rating);
        
        $model = new RatingAndReview(['rating' => 1]);
        $this->assertEquals(1, $model->rating);
    }

    public function test_language_validation()
    {
        // Test that original_language accepts valid values
        $model = new RatingAndReview(['original_language' => 'en']);
        $this->assertEquals('en', $model->original_language);
        
        $model = new RatingAndReview(['original_language' => 'ar']);
        $this->assertEquals('ar', $model->original_language);
    }

    public function test_publication_status_validation()
    {
        $validStatuses = ['pending', 'published', 'rejected'];
        
        foreach ($validStatuses as $status) {
            $model = new RatingAndReview(['publication_status' => $status]);
            $this->assertEquals($status, $model->publication_status);
        }
    }

    public function test_country_code_format()
    {
        $model = new RatingAndReview(['country' => 'AE']);
        $this->assertEquals('AE', $model->country);
        $this->assertEquals(2, strlen($model->country));
    }

    public function test_created_at_format()
    {
        $model = new RatingAndReview();
        
        // Should be in ISO8601 format
        $this->assertMatchesRegularExpression(
            '/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}/',
            $model->getAttributes()['created_at']
        );
    }

    public function test_review_id_is_uuid_format()
    {
        $model = new RatingAndReview();
        
        $this->assertTrue(Str::isUuid($model->review_id));
    }
}
