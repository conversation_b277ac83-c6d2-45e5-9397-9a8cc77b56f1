<?php

namespace Tests\Unit\Models;

use App\Models\RatingsAndReviewStatistics;
use PHPUnit\Framework\TestCase;

class RatingsAndReviewStatisticsTest extends TestCase
{
    public function test_model_has_correct_table_name()
    {
        $model = new RatingsAndReviewStatistics();
        $this->assertEquals('ratings_and_review_statistics', $model->getTable());
    }

    public function test_model_has_correct_primary_key()
    {
        $model = new RatingsAndReviewStatistics();
        $this->assertEquals('product_id', $model->getKeyName());
        $this->assertFalse($model->getIncrementing());
    }

    public function test_model_has_correct_fillable_attributes()
    {
        $model = new RatingsAndReviewStatistics();
        $expectedFillable = [
            'product_id',
            'rating_count',
            'average_rating',
            'rating_distribution',
            'percentage_distribution',
            'last_calculated_at',
        ];
        
        $this->assertEquals($expectedFillable, $model->getFillable());
    }

    public function test_model_has_correct_casts()
    {
        $model = new RatingsAndReviewStatistics();
        $expectedCasts = [
            'rating_count' => 'integer',
            'average_rating' => 'float',
            'rating_distribution' => 'array',
            'percentage_distribution' => 'array',
            'last_calculated_at' => 'datetime',
        ];
        
        foreach ($expectedCasts as $attribute => $cast) {
            $this->assertEquals($cast, $model->getCasts()[$attribute]);
        }
    }

    public function test_rating_count_cast_to_integer()
    {
        $model = new RatingsAndReviewStatistics(['rating_count' => '100']);
        $this->assertIsInt($model->rating_count);
        $this->assertEquals(100, $model->rating_count);
    }

    public function test_average_rating_cast_to_float()
    {
        $model = new RatingsAndReviewStatistics(['average_rating' => '4.5']);
        $this->assertIsFloat($model->average_rating);
        $this->assertEquals(4.5, $model->average_rating);
    }

    public function test_rating_distribution_cast_to_array()
    {
        $distribution = [1 => 5, 2 => 10, 3 => 15, 4 => 30, 5 => 40];
        $model = new RatingsAndReviewStatistics(['rating_distribution' => $distribution]);
        
        $this->assertIsArray($model->rating_distribution);
        $this->assertEquals($distribution, $model->rating_distribution);
    }

    public function test_percentage_distribution_cast_to_array()
    {
        $percentages = [1 => 5.0, 2 => 10.0, 3 => 15.0, 4 => 30.0, 5 => 40.0];
        $model = new RatingsAndReviewStatistics(['percentage_distribution' => $percentages]);
        
        $this->assertIsArray($model->percentage_distribution);
        $this->assertEquals($percentages, $model->percentage_distribution);
    }

    public function test_last_calculated_at_cast_to_datetime()
    {
        $model = new RatingsAndReviewStatistics(['last_calculated_at' => '2024-01-01T12:00:00Z']);
        
        $this->assertInstanceOf(\Carbon\Carbon::class, $model->last_calculated_at);
    }

    public function test_model_can_be_instantiated_with_all_attributes()
    {
        $attributes = [
            'product_id' => 'product-123',
            'rating_count' => 100,
            'average_rating' => 4.2,
            'rating_distribution' => [1 => 2, 2 => 5, 3 => 13, 4 => 35, 5 => 45],
            'percentage_distribution' => [1 => 2.0, 2 => 5.0, 3 => 13.0, 4 => 35.0, 5 => 45.0],
            'last_calculated_at' => now()->toIso8601String(),
        ];
        
        $model = new RatingsAndReviewStatistics($attributes);
        
        $this->assertEquals('product-123', $model->product_id);
        $this->assertEquals(100, $model->rating_count);
        $this->assertEquals(4.2, $model->average_rating);
        $this->assertIsArray($model->rating_distribution);
        $this->assertIsArray($model->percentage_distribution);
        $this->assertInstanceOf(\Carbon\Carbon::class, $model->last_calculated_at);
    }

    public function test_model_validates_rating_distribution_structure()
    {
        $validDistribution = [1 => 5, 2 => 10, 3 => 15, 4 => 30, 5 => 40];
        $model = new RatingsAndReviewStatistics(['rating_distribution' => $validDistribution]);
        
        // Check that all rating levels (1-5) are present
        foreach ([1, 2, 3, 4, 5] as $rating) {
            $this->assertArrayHasKey($rating, $model->rating_distribution);
            $this->assertIsInt($model->rating_distribution[$rating]);
        }
    }

    public function test_model_validates_percentage_distribution_structure()
    {
        $validPercentages = [1 => 5.0, 2 => 10.0, 3 => 15.0, 4 => 30.0, 5 => 40.0];
        $model = new RatingsAndReviewStatistics(['percentage_distribution' => $validPercentages]);
        
        // Check that all rating levels (1-5) are present
        foreach ([1, 2, 3, 4, 5] as $rating) {
            $this->assertArrayHasKey($rating, $model->percentage_distribution);
            $this->assertIsFloat($model->percentage_distribution[$rating]);
        }
    }

    public function test_model_handles_zero_rating_count()
    {
        $model = new RatingsAndReviewStatistics([
            'product_id' => 'product-no-reviews',
            'rating_count' => 0,
            'average_rating' => 0.0,
            'rating_distribution' => [1 => 0, 2 => 0, 3 => 0, 4 => 0, 5 => 0],
            'percentage_distribution' => [1 => 0.0, 2 => 0.0, 3 => 0.0, 4 => 0.0, 5 => 0.0],
        ]);
        
        $this->assertEquals(0, $model->rating_count);
        $this->assertEquals(0.0, $model->average_rating);
        $this->assertEquals(0, array_sum($model->rating_distribution));
        $this->assertEquals(0.0, array_sum($model->percentage_distribution));
    }

    public function test_model_connection_is_dynamodb()
    {
        $model = new RatingsAndReviewStatistics();
        $this->assertEquals('dynamodb', $model->getConnectionName());
    }

    public function test_model_timestamps_disabled()
    {
        $model = new RatingsAndReviewStatistics();
        $this->assertFalse($model->timestamps);
    }

    public function test_average_rating_precision()
    {
        // Test that average rating maintains proper precision
        $model = new RatingsAndReviewStatistics(['average_rating' => 4.567]);
        $this->assertEquals(4.567, $model->average_rating);
        
        $model = new RatingsAndReviewStatistics(['average_rating' => 4.0]);
        $this->assertEquals(4.0, $model->average_rating);
    }

    public function test_percentage_distribution_precision()
    {
        $percentages = [1 => 5.67, 2 => 10.33, 3 => 15.0, 4 => 30.5, 5 => 38.5];
        $model = new RatingsAndReviewStatistics(['percentage_distribution' => $percentages]);
        
        foreach ($percentages as $rating => $percentage) {
            $this->assertEquals($percentage, $model->percentage_distribution[$rating]);
        }
    }

    public function test_product_id_is_string()
    {
        $model = new RatingsAndReviewStatistics(['product_id' => 'product-123']);
        $this->assertIsString($model->product_id);
        $this->assertEquals('product-123', $model->product_id);
    }

    public function test_model_can_handle_large_numbers()
    {
        $model = new RatingsAndReviewStatistics([
            'rating_count' => 999999,
            'rating_distribution' => [1 => 50000, 2 => 100000, 3 => 200000, 4 => 300000, 5 => 349999],
        ]);
        
        $this->assertEquals(999999, $model->rating_count);
        $this->assertEquals(999999, array_sum($model->rating_distribution));
    }

    public function test_model_validates_distribution_consistency()
    {
        // Test that rating_count matches sum of rating_distribution
        $distribution = [1 => 10, 2 => 20, 3 => 30, 4 => 40, 5 => 50];
        $expectedTotal = array_sum($distribution); // 150
        
        $model = new RatingsAndReviewStatistics([
            'rating_count' => $expectedTotal,
            'rating_distribution' => $distribution,
        ]);
        
        $this->assertEquals($expectedTotal, $model->rating_count);
        $this->assertEquals($expectedTotal, array_sum($model->rating_distribution));
    }

    public function test_model_validates_percentage_sum()
    {
        // Test that percentages sum to approximately 100%
        $percentages = [1 => 10.0, 2 => 20.0, 3 => 30.0, 4 => 25.0, 5 => 15.0];
        $model = new RatingsAndReviewStatistics(['percentage_distribution' => $percentages]);
        
        $sum = array_sum($model->percentage_distribution);
        $this->assertEquals(100.0, $sum);
    }
}
