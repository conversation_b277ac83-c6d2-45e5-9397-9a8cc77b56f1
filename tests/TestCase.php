<?php

namespace Tests;

use Illuminate\Foundation\Testing\TestCase as BaseTestCase;

abstract class TestCase extends BaseTestCase
{

    /**
     * Setup the test environment.
     */
    protected function setUp(): void
    {
        parent::setUp();

        // Set up test environment configurations
        $this->setupTestEnvironment();
    }

    /**
     * Configure the test environment.
     */
    protected function setupTestEnvironment(): void
    {
        // Configure DynamoDB for testing
        config([
            'database.connections.dynamodb.local_endpoint' => env('DYNAMODB_LOCAL_ENDPOINT', 'http://localhost:8000'),
            'database.connections.dynamodb.local' => true,
            'database.connections.dynamodb.debug' => false,
        ]);

        // Configure external services for testing
        config([
            'services.google_translate.api_key' => env('GOOGLE_TRANSLATE_API_KEY', 'testing-key'),
            'services.google_translate.endpoint' => 'https://translation.googleapis.com/language/translate/v2',
            'services.cloudfront.distribution_id' => env('CLOUDFRONT_DISTRIBUTION_ID', 'testing-distribution'),
            'filesystems.default' => env('FILESYSTEM_DISK', 'local'),
        ]);

        // Disable external API calls in testing
        config([
            'app.disable_external_apis' => true,
        ]);
    }

    /**
     * Create a mock for external services.
     */
    protected function mockExternalServices(): void
    {
        // This method can be called in tests that need to mock external services
        $this->mock(\App\Services\CloudFrontService::class, function ($mock) {
            $mock->shouldReceive('invalidateProductReviewsApi')->andReturn(true);
            $mock->shouldReceive('invalidateReviewApi')->andReturn(true);
            $mock->shouldReceive('invalidateAllReviewsApi')->andReturn(true);
        });

        $this->mock(\App\Services\TranslationService::class, function ($mock) {
            $mock->shouldReceive('translateReview')->andReturn(true);
            $mock->shouldReceive('batchTranslateReviews')->andReturn(true);
        });
    }

    /**
     * Assert that a JSON response has the expected structure.
     */
    protected function assertJsonResponseStructure(array $structure, $response): void
    {
        $response->assertJsonStructure($structure);
    }

    /**
     * Assert that a review has the expected attributes.
     */
    protected function assertReviewAttributes(array $expected, array $actual): void
    {
        foreach ($expected as $key => $value) {
            $this->assertEquals($value, $actual[$key], "Review attribute '{$key}' does not match expected value");
        }
    }

    /**
     * Create a test review with default attributes.
     */
    protected function createTestReview(array $attributes = []): \App\Models\RatingAndReview
    {
        $factory = new \Database\Factories\RatingAndReviewFactory();

        $defaultAttributes = [
            'user_id' => 'test_user_' . uniqid(),
            'product_id' => 'test_product_' . uniqid(),
            'rating' => 5,
            'original_language' => 'en',
            'review_en' => 'This is a test review',
            'country' => 'AE',
            'publication_status' => 'published',
        ];

        return $factory->create(array_merge($defaultAttributes, $attributes));
    }

    /**
     * Create test statistics for a product.
     */
    protected function createTestStatistics(array $attributes = []): \App\Models\RatingsAndReviewStatistics
    {
        $factory = new \Database\Factories\RatingsAndReviewStatisticsFactory();

        $defaultAttributes = [
            'product_id' => 'test_product_' . uniqid(),
            'rating_count' => 10,
            'average_rating' => 4.5,
            'rating_distribution' => [1 => 0, 2 => 1, 3 => 2, 4 => 3, 5 => 4],
            'percentage_distribution' => [1 => 0.0, 2 => 10.0, 3 => 20.0, 4 => 30.0, 5 => 40.0],
        ];

        return $factory->create(array_merge($defaultAttributes, $attributes));
    }

    /**
     * Assert that pagination data is correct.
     */
    protected function assertPaginationStructure($response, ?int $expectedCount = null): void
    {
        $response->assertJsonStructure([
            'pagination' => [
                'current_page',
                'per_page',
                'total',
                'has_more_pages',
            ]
        ]);

        if ($expectedCount !== null) {
            $this->assertCount($expectedCount, $response->json('data'));
        }
    }

    /**
     * Assert that a review resource has the correct structure.
     */
    protected function assertReviewResourceStructure($response): void
    {
        $response->assertJsonStructure([
            'data' => [
                'review_id',
                'user_id',
                'product_id',
                'rating',
                'original_language',
                'review_en',
                'review_ar',
                'country',
                'created_at',
                'media',
                'publication_status',
            ]
        ]);
    }

    /**
     * Assert that statistics resource has the correct structure.
     */
    protected function assertStatisticsResourceStructure($response): void
    {
        $response->assertJsonStructure([
            'data' => [
                'product_id',
                'rating_count',
                'average_rating',
                'rating_distribution',
                'percentage_distribution',
                'last_calculated_at',
            ]
        ]);
    }

    /**
     * Create multiple test reviews for a product.
     */
    protected function createMultipleReviewsForProduct(string $productId, int $count = 5, array $baseAttributes = []): array
    {
        $reviews = [];
        $factory = new \Database\Factories\RatingAndReviewFactory();

        for ($i = 0; $i < $count; $i++) {
            $attributes = array_merge([
                'product_id' => $productId,
                'rating' => rand(1, 5),
                'publication_status' => 'published',
            ], $baseAttributes);

            $reviews[] = $factory->create($attributes);
        }

        return $reviews;
    }

    /**
     * Assert that a UUID is valid.
     */
    protected function assertValidUuid(string $uuid): void
    {
        $this->assertMatchesRegularExpression(
            '/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i',
            $uuid,
            'Invalid UUID format'
        );
    }

    /**
     * Assert that a timestamp is in ISO8601 format.
     */
    protected function assertValidIso8601Timestamp(string $timestamp): void
    {
        $this->assertMatchesRegularExpression(
            '/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}/',
            $timestamp,
            'Invalid ISO8601 timestamp format'
        );
    }

    /**
     * Get a test file for upload testing.
     */
    protected function getTestUploadFile(string $type = 'image', string $extension = 'jpg'): \Illuminate\Http\UploadedFile
    {
        if ($type === 'image') {
            return \Illuminate\Http\UploadedFile::fake()->image("test.{$extension}", 800, 600);
        } elseif ($type === 'video') {
            return \Illuminate\Http\UploadedFile::fake()->create("test.{$extension}", 5000, 'video/mp4');
        }

        return \Illuminate\Http\UploadedFile::fake()->create("test.{$extension}", 1000);
    }

    /**
     * Clean up after tests.
     */
    protected function tearDown(): void
    {
        // Clean up any test data if needed
        parent::tearDown();
    }
}
