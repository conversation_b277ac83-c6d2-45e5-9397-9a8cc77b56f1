<?php

namespace Tests\Feature\Customer;

use App\Models\RatingAndReview;
use App\Services\CloudFrontService;
use App\Services\MediaUploadService;
use Database\Factories\RatingAndReviewFactory;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Queue;
use Illuminate\Support\Facades\Storage;
use Tests\TestCase;
use Mockery;

class ReviewCreationTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Mock external services to avoid actual API calls during testing
        $this->mock(CloudFrontService::class, function ($mock) {
            $mock->shouldReceive('invalidateProductReviewsApi')->andReturn(true);
        });
        
        Queue::fake();
        Storage::fake('local');
    }

    public function test_can_create_review_with_valid_data()
    {
        $reviewData = [
            'user_id' => 'user_123',
            'product_id' => 'product_456',
            'rating' => 5,
            'original_language' => 'en',
            'review_en' => 'This is an excellent product! Highly recommended.',
            'country' => 'AE',
        ];

        $response = $this->postJson('/api/reviews', $reviewData);

        $response->assertStatus(201)
            ->assertJsonStructure([
                'data' => [
                    'review_id',
                    'user_id',
                    'product_id',
                    'rating',
                    'original_language',
                    'review_en',
                    'review_ar',
                    'country',
                    'created_at',
                    'media',
                    'publication_status',
                ]
            ]);

        $this->assertEquals('user_123', $response->json('data.user_id'));
        $this->assertEquals('product_456', $response->json('data.product_id'));
        $this->assertEquals(5, $response->json('data.rating'));
        $this->assertEquals('en', $response->json('data.original_language'));
        $this->assertEquals('This is an excellent product! Highly recommended.', $response->json('data.review_en'));
        $this->assertEquals('AE', $response->json('data.country'));
        $this->assertEquals('pending', $response->json('data.publication_status'));
        $this->assertEquals([], $response->json('data.media'));
    }

    public function test_can_create_arabic_review()
    {
        $reviewData = [
            'user_id' => 'user_123',
            'product_id' => 'product_456',
            'rating' => 4,
            'original_language' => 'ar',
            'review_ar' => 'منتج ممتاز! أنصح به بشدة.',
            'country' => 'SA',
        ];

        $response = $this->postJson('/api/reviews', $reviewData);

        $response->assertStatus(201);
        $this->assertEquals('ar', $response->json('data.original_language'));
        $this->assertEquals('منتج ممتاز! أنصح به بشدة.', $response->json('data.review_ar'));
        $this->assertNull($response->json('data.review_en'));
    }

    public function test_can_create_review_with_media_files()
    {
        // Mock the MediaUploadService
        $this->mock(MediaUploadService::class, function ($mock) {
            $mock->shouldReceive('uploadMedia')
                ->andReturn([
                    'id' => 'media-test123',
                    'type' => 'image',
                    'path' => 'reviews/test/media-test123.jpg',
                    'url' => 'http://localhost/files/reviews/test/media-test123.jpg',
                ]);
        });

        $reviewData = [
            'user_id' => 'user_123',
            'product_id' => 'product_456',
            'rating' => 5,
            'original_language' => 'en',
            'review_en' => 'Great product with photos!',
            'country' => 'AE',
        ];

        $file = UploadedFile::fake()->image('product.jpg', 800, 600);

        $response = $this->postJson('/api/reviews', array_merge($reviewData, [
            'media_files' => [$file]
        ]));

        $response->assertStatus(201);
        $this->assertNotEmpty($response->json('data.media'));
        $this->assertCount(1, $response->json('data.media'));
    }

    public function test_validation_fails_for_missing_required_fields()
    {
        $response = $this->postJson('/api/reviews', []);

        $response->assertStatus(422)
            ->assertJsonValidationErrors([
                'user_id',
                'product_id',
                'rating',
                'original_language',
                'country',
            ]);
    }

    public function test_validation_fails_for_invalid_rating()
    {
        $reviewData = [
            'user_id' => 'user_123',
            'product_id' => 'product_456',
            'rating' => 6, // Invalid rating (should be 1-5)
            'original_language' => 'en',
            'review_en' => 'Test review',
            'country' => 'AE',
        ];

        $response = $this->postJson('/api/reviews', $reviewData);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['rating']);
    }

    public function test_validation_fails_for_invalid_language()
    {
        $reviewData = [
            'user_id' => 'user_123',
            'product_id' => 'product_456',
            'rating' => 5,
            'original_language' => 'fr', // Invalid language (should be en or ar)
            'review_en' => 'Test review',
            'country' => 'AE',
        ];

        $response = $this->postJson('/api/reviews', $reviewData);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['original_language']);
    }

    public function test_validation_fails_for_invalid_country_code()
    {
        $reviewData = [
            'user_id' => 'user_123',
            'product_id' => 'product_456',
            'rating' => 5,
            'original_language' => 'en',
            'review_en' => 'Test review',
            'country' => 'USA', // Invalid country code (should be 2 characters)
        ];

        $response = $this->postJson('/api/reviews', $reviewData);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['country']);
    }

    public function test_validation_requires_review_text_for_original_language()
    {
        // Test English review without review_en
        $reviewData = [
            'user_id' => 'user_123',
            'product_id' => 'product_456',
            'rating' => 5,
            'original_language' => 'en',
            'country' => 'AE',
        ];

        $response = $this->postJson('/api/reviews', $reviewData);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['review_en']);

        // Test Arabic review without review_ar
        $reviewData['original_language'] = 'ar';
        unset($reviewData['review_en']);

        $response = $this->postJson('/api/reviews', $reviewData);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['review_ar']);
    }

    public function test_validation_fails_for_invalid_media_file_type()
    {
        $reviewData = [
            'user_id' => 'user_123',
            'product_id' => 'product_456',
            'rating' => 5,
            'original_language' => 'en',
            'review_en' => 'Test review',
            'country' => 'AE',
        ];

        $invalidFile = UploadedFile::fake()->create('document.pdf', 1000, 'application/pdf');

        $response = $this->postJson('/api/reviews', array_merge($reviewData, [
            'media_files' => [$invalidFile]
        ]));

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['media_files.0']);
    }

    public function test_validation_fails_for_oversized_media_file()
    {
        $reviewData = [
            'user_id' => 'user_123',
            'product_id' => 'product_456',
            'rating' => 5,
            'original_language' => 'en',
            'review_en' => 'Test review',
            'country' => 'AE',
        ];

        // Create a file larger than 10MB (10240 KB)
        $oversizedFile = UploadedFile::fake()->create('large_image.jpg', 15000, 'image/jpeg');

        $response = $this->postJson('/api/reviews', array_merge($reviewData, [
            'media_files' => [$oversizedFile]
        ]));

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['media_files.0']);
    }

    public function test_review_gets_default_values()
    {
        $reviewData = [
            'user_id' => 'user_123',
            'product_id' => 'product_456',
            'rating' => 5,
            'original_language' => 'en',
            'review_en' => 'Test review',
            'country' => 'AE',
        ];

        $response = $this->postJson('/api/reviews', $reviewData);

        $response->assertStatus(201);
        
        // Check default values
        $this->assertEquals('pending', $response->json('data.publication_status'));
        $this->assertEquals([], $response->json('data.media'));
        $this->assertNotNull($response->json('data.review_id'));
        $this->assertNotNull($response->json('data.created_at'));
    }

    public function test_review_id_is_uuid_format()
    {
        $reviewData = [
            'user_id' => 'user_123',
            'product_id' => 'product_456',
            'rating' => 5,
            'original_language' => 'en',
            'review_en' => 'Test review',
            'country' => 'AE',
        ];

        $response = $this->postJson('/api/reviews', $reviewData);

        $response->assertStatus(201);
        
        $reviewId = $response->json('data.review_id');
        $this->assertMatchesRegularExpression(
            '/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i',
            $reviewId
        );
    }

    public function test_cloudfront_invalidation_is_triggered()
    {
        $cloudFrontService = $this->mock(CloudFrontService::class);
        $cloudFrontService->shouldReceive('invalidateProductReviewsApi')
            ->once()
            ->with('product_456');

        $reviewData = [
            'user_id' => 'user_123',
            'product_id' => 'product_456',
            'rating' => 5,
            'original_language' => 'en',
            'review_en' => 'Test review',
            'country' => 'AE',
        ];

        $response = $this->postJson('/api/reviews', $reviewData);

        $response->assertStatus(201);
    }
}
