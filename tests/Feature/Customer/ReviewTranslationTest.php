<?php

namespace Tests\Feature\Customer;

use App\Models\RatingAndReview;
use App\Services\TranslationService;
use Database\Factories\RatingAndReviewFactory;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\Client\Response;
use Illuminate\Support\Facades\Http;
use Tests\TestCase;
use Mockery;

class ReviewTranslationTest extends TestCase
{
    use RefreshDatabase;

    protected RatingAndReviewFactory $reviewFactory;

    protected function setUp(): void
    {
        parent::setUp();
        $this->reviewFactory = new RatingAndReviewFactory();
    }

    public function test_can_get_translated_review_when_translation_exists()
    {
        // Create a review with both English and Arabic text
        $review = $this->reviewFactory->create([
            'original_language' => 'en',
            'review_en' => 'This is an excellent product!',
            'review_ar' => 'هذا منتج ممتاز!',
            'publication_status' => 'published',
        ]);

        $response = $this->getJson("/api/reviews/{$review->review_id}/translate?target_language=ar");

        $response->assertStatus(200)
            ->assertJsonStructure([
                'data' => [
                    'review_id',
                    'user_id',
                    'product_id',
                    'rating',
                    'original_language',
                    'review_en',
                    'review_ar',
                    'country',
                    'created_at',
                    'media',
                    'publication_status',
                ]
            ]);

        $this->assertEquals('هذا منتج ممتاز!', $response->json('data.review_ar'));
        $this->assertEquals('This is an excellent product!', $response->json('data.review_en'));
    }

    public function test_can_translate_review_on_demand_when_translation_missing()
    {
        // Mock the translation service
        $this->mock(TranslationService::class, function ($mock) {
            $mock->shouldReceive('translateReview')
                ->once()
                ->andReturn(true);
        });

        // Create a review with only English text
        $review = $this->reviewFactory->create([
            'original_language' => 'en',
            'review_en' => 'This is an excellent product!',
            'review_ar' => null,
            'publication_status' => 'published',
        ]);

        // Mock HTTP response for Google Translate API
        Http::fake([
            'translation.googleapis.com/*' => Http::response([
                'data' => [
                    'translations' => [
                        ['translatedText' => 'هذا منتج ممتاز!']
                    ]
                ]
            ], 200)
        ]);

        $response = $this->getJson("/api/reviews/{$review->review_id}/translate?target_language=ar");

        $response->assertStatus(200);
        $this->assertNotNull($response->json('data.review_ar'));
    }

    public function test_translation_validation_requires_target_language()
    {
        $review = $this->reviewFactory->create(['publication_status' => 'published']);

        $response = $this->getJson("/api/reviews/{$review->review_id}/translate");

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['target_language']);
    }

    public function test_translation_validation_requires_valid_target_language()
    {
        $review = $this->reviewFactory->create(['publication_status' => 'published']);

        $response = $this->getJson("/api/reviews/{$review->review_id}/translate?target_language=fr");

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['target_language']);
    }

    public function test_translation_returns_404_for_nonexistent_review()
    {
        $response = $this->getJson('/api/reviews/nonexistent-review-id/translate?target_language=ar');

        $response->assertStatus(404)
            ->assertJson([
                'message' => 'Review not found.'
            ]);
    }

    public function test_translation_returns_404_for_unpublished_review()
    {
        $review = $this->reviewFactory->create(['publication_status' => 'pending']);

        $response = $this->getJson("/api/reviews/{$review->review_id}/translate?target_language=ar");

        $response->assertStatus(404)
            ->assertJson([
                'message' => 'Review not found.'
            ]);
    }

    public function test_can_translate_english_to_arabic()
    {
        // Mock the translation service
        $this->mock(TranslationService::class, function ($mock) {
            $mock->shouldReceive('translateReview')
                ->once()
                ->with(Mockery::on(function ($review) {
                    return $review->original_language === 'en' && 
                           $review->review_en === 'Great product!' &&
                           $review->review_ar === null;
                }))
                ->andReturnUsing(function ($review) {
                    $review->review_ar = 'منتج رائع!';
                    $review->save();
                    return true;
                });
        });

        $review = $this->reviewFactory->create([
            'original_language' => 'en',
            'review_en' => 'Great product!',
            'review_ar' => null,
            'publication_status' => 'published',
        ]);

        $response = $this->getJson("/api/reviews/{$review->review_id}/translate?target_language=ar");

        $response->assertStatus(200);
        $this->assertEquals('منتج رائع!', $response->json('data.review_ar'));
    }

    public function test_can_translate_arabic_to_english()
    {
        // Mock the translation service
        $this->mock(TranslationService::class, function ($mock) {
            $mock->shouldReceive('translateReview')
                ->once()
                ->with(Mockery::on(function ($review) {
                    return $review->original_language === 'ar' && 
                           $review->review_ar === 'منتج رائع!' &&
                           $review->review_en === null;
                }))
                ->andReturnUsing(function ($review) {
                    $review->review_en = 'Great product!';
                    $review->save();
                    return true;
                });
        });

        $review = $this->reviewFactory->create([
            'original_language' => 'ar',
            'review_ar' => 'منتج رائع!',
            'review_en' => null,
            'publication_status' => 'published',
        ]);

        $response = $this->getJson("/api/reviews/{$review->review_id}/translate?target_language=en");

        $response->assertStatus(200);
        $this->assertEquals('Great product!', $response->json('data.review_en'));
    }

    public function test_translation_handles_service_failure_gracefully()
    {
        // Mock the translation service to fail
        $this->mock(TranslationService::class, function ($mock) {
            $mock->shouldReceive('translateReview')
                ->once()
                ->andReturn(false);
        });

        $review = $this->reviewFactory->create([
            'original_language' => 'en',
            'review_en' => 'Great product!',
            'review_ar' => null,
            'publication_status' => 'published',
        ]);

        $response = $this->getJson("/api/reviews/{$review->review_id}/translate?target_language=ar");

        $response->assertStatus(500)
            ->assertJson([
                'message' => 'Translation failed. Please try again later.'
            ]);
    }

    public function test_translation_skips_when_requesting_same_language_as_original()
    {
        $review = $this->reviewFactory->create([
            'original_language' => 'en',
            'review_en' => 'Great product!',
            'publication_status' => 'published',
        ]);

        $response = $this->getJson("/api/reviews/{$review->review_id}/translate?target_language=en");

        $response->assertStatus(200);
        // Should return the review without attempting translation
        $this->assertEquals('Great product!', $response->json('data.review_en'));
    }

    public function test_translation_returns_existing_translation_without_api_call()
    {
        // Create a review that already has both translations
        $review = $this->reviewFactory->create([
            'original_language' => 'en',
            'review_en' => 'Great product!',
            'review_ar' => 'منتج رائع!',
            'publication_status' => 'published',
        ]);

        // Mock the translation service to ensure it's not called
        $this->mock(TranslationService::class, function ($mock) {
            $mock->shouldNotReceive('translateReview');
        });

        $response = $this->getJson("/api/reviews/{$review->review_id}/translate?target_language=ar");

        $response->assertStatus(200);
        $this->assertEquals('منتج رائع!', $response->json('data.review_ar'));
        $this->assertEquals('Great product!', $response->json('data.review_en'));
    }

    public function test_translation_preserves_all_review_attributes()
    {
        $review = $this->reviewFactory->create([
            'original_language' => 'en',
            'review_en' => 'Great product!',
            'review_ar' => 'منتج رائع!',
            'publication_status' => 'published',
            'rating' => 5,
            'country' => 'AE',
            'media' => [
                [
                    'id' => 'media-123',
                    'type' => 'image',
                    'url' => 'https://example.com/image.jpg'
                ]
            ]
        ]);

        $response = $this->getJson("/api/reviews/{$review->review_id}/translate?target_language=ar");

        $response->assertStatus(200);
        
        $data = $response->json('data');
        $this->assertEquals($review->review_id, $data['review_id']);
        $this->assertEquals($review->user_id, $data['user_id']);
        $this->assertEquals($review->product_id, $data['product_id']);
        $this->assertEquals($review->rating, $data['rating']);
        $this->assertEquals($review->country, $data['country']);
        $this->assertEquals($review->media, $data['media']);
        $this->assertEquals($review->publication_status, $data['publication_status']);
    }

    public function test_translation_handles_empty_source_text()
    {
        $review = $this->reviewFactory->create([
            'original_language' => 'en',
            'review_en' => '',
            'review_ar' => null,
            'publication_status' => 'published',
        ]);

        $response = $this->getJson("/api/reviews/{$review->review_id}/translate?target_language=ar");

        $response->assertStatus(400)
            ->assertJson([
                'message' => 'No content available for translation.'
            ]);
    }

    public function test_translation_endpoint_rate_limiting()
    {
        // This test would verify rate limiting if implemented
        // For now, we'll just test that the endpoint is accessible
        $review = $this->reviewFactory->create([
            'original_language' => 'en',
            'review_en' => 'Great product!',
            'review_ar' => 'منتج رائع!',
            'publication_status' => 'published',
        ]);

        $response = $this->getJson("/api/reviews/{$review->review_id}/translate?target_language=ar");
        $response->assertStatus(200);
    }
}
