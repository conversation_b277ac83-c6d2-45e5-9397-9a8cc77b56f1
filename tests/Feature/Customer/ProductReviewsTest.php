<?php

namespace Tests\Feature\Customer;

use App\Models\RatingAndReview;
use App\Models\RatingsAndReviewStatistics;
use Database\Factories\RatingAndReviewFactory;
use Database\Factories\RatingsAndReviewStatisticsFactory;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class ProductReviewsTest extends TestCase
{
    use RefreshDatabase;

    protected RatingAndReviewFactory $reviewFactory;
    protected RatingsAndReviewStatisticsFactory $statisticsFactory;

    protected function setUp(): void
    {
        parent::setUp();
        $this->reviewFactory = new RatingAndReviewFactory();
        $this->statisticsFactory = new RatingsAndReviewStatisticsFactory();
    }

    public function test_can_get_product_reviews()
    {
        $productId = 'product_123';
        
        // Create some reviews for the product
        $review1 = $this->reviewFactory->create([
            'product_id' => $productId,
            'publication_status' => 'published',
            'rating' => 5,
            'original_language' => 'en',
            'review_en' => 'Excellent product!',
            'country' => 'AE',
        ]);
        
        $review2 = $this->reviewFactory->create([
            'product_id' => $productId,
            'publication_status' => 'published',
            'rating' => 4,
            'original_language' => 'ar',
            'review_ar' => 'منتج جيد جداً',
            'country' => 'SA',
        ]);

        // Create statistics for the product
        $statistics = $this->statisticsFactory->create([
            'product_id' => $productId,
            'rating_count' => 2,
            'average_rating' => 4.5,
            'rating_distribution' => [1 => 0, 2 => 0, 3 => 0, 4 => 1, 5 => 1],
            'percentage_distribution' => [1 => 0.0, 2 => 0.0, 3 => 0.0, 4 => 50.0, 5 => 50.0],
        ]);

        $response = $this->getJson("/api/products/{$productId}/reviews");

        $response->assertStatus(200)
            ->assertJsonStructure([
                'data' => [
                    'reviews' => [
                        '*' => [
                            'review_id',
                            'user_id',
                            'product_id',
                            'rating',
                            'original_language',
                            'review_en',
                            'review_ar',
                            'country',
                            'created_at',
                            'media',
                            'publication_status',
                        ]
                    ],
                    'statistics' => [
                        'product_id',
                        'rating_count',
                        'average_rating',
                        'rating_distribution',
                        'percentage_distribution',
                        'last_calculated_at',
                    ],
                    'pagination' => [
                        'current_page',
                        'per_page',
                        'total',
                        'has_more_pages',
                    ]
                ]
            ]);

        $this->assertCount(2, $response->json('data.reviews'));
        $this->assertEquals(4.5, $response->json('data.statistics.average_rating'));
        $this->assertEquals(2, $response->json('data.statistics.rating_count'));
    }

    public function test_only_returns_published_reviews()
    {
        $productId = 'product_123';
        
        // Create reviews with different statuses
        $publishedReview = $this->reviewFactory->create([
            'product_id' => $productId,
            'publication_status' => 'published',
        ]);
        
        $pendingReview = $this->reviewFactory->create([
            'product_id' => $productId,
            'publication_status' => 'pending',
        ]);
        
        $rejectedReview = $this->reviewFactory->create([
            'product_id' => $productId,
            'publication_status' => 'rejected',
        ]);

        $response = $this->getJson("/api/products/{$productId}/reviews");

        $response->assertStatus(200);
        
        // Should only return the published review
        $this->assertCount(1, $response->json('data.reviews'));
        $this->assertEquals('published', $response->json('data.reviews.0.publication_status'));
    }

    public function test_can_filter_reviews_by_country()
    {
        $productId = 'product_123';
        
        // Create reviews from different countries
        $aeReview = $this->reviewFactory->create([
            'product_id' => $productId,
            'publication_status' => 'published',
            'country' => 'AE',
        ]);
        
        $saReview = $this->reviewFactory->create([
            'product_id' => $productId,
            'publication_status' => 'published',
            'country' => 'SA',
        ]);

        $response = $this->getJson("/api/products/{$productId}/reviews?country=AE");

        $response->assertStatus(200);
        
        // Should only return reviews from AE
        $this->assertCount(1, $response->json('data.reviews'));
        $this->assertEquals('AE', $response->json('data.reviews.0.country'));
    }

    public function test_can_filter_reviews_by_rating()
    {
        $productId = 'product_123';
        
        // Create reviews with different ratings
        $fiveStarReview = $this->reviewFactory->create([
            'product_id' => $productId,
            'publication_status' => 'published',
            'rating' => 5,
        ]);
        
        $fourStarReview = $this->reviewFactory->create([
            'product_id' => $productId,
            'publication_status' => 'published',
            'rating' => 4,
        ]);

        $response = $this->getJson("/api/products/{$productId}/reviews?rating=5");

        $response->assertStatus(200);
        
        // Should only return 5-star reviews
        $this->assertCount(1, $response->json('data.reviews'));
        $this->assertEquals(5, $response->json('data.reviews.0.rating'));
    }

    public function test_can_filter_reviews_by_language()
    {
        $productId = 'product_123';
        
        // Create reviews in different languages
        $englishReview = $this->reviewFactory->create([
            'product_id' => $productId,
            'publication_status' => 'published',
            'original_language' => 'en',
        ]);
        
        $arabicReview = $this->reviewFactory->create([
            'product_id' => $productId,
            'publication_status' => 'published',
            'original_language' => 'ar',
        ]);

        $response = $this->getJson("/api/products/{$productId}/reviews?language=en");

        $response->assertStatus(200);
        
        // Should only return English reviews
        $this->assertCount(1, $response->json('data.reviews'));
        $this->assertEquals('en', $response->json('data.reviews.0.original_language'));
    }

    public function test_pagination_works_correctly()
    {
        $productId = 'product_123';
        
        // Create multiple reviews
        for ($i = 0; $i < 25; $i++) {
            $this->reviewFactory->create([
                'product_id' => $productId,
                'publication_status' => 'published',
            ]);
        }

        // Test first page
        $response = $this->getJson("/api/products/{$productId}/reviews?per_page=10");

        $response->assertStatus(200);
        $this->assertCount(10, $response->json('data.reviews'));
        $this->assertEquals(1, $response->json('data.pagination.current_page'));
        $this->assertEquals(10, $response->json('data.pagination.per_page'));
        $this->assertEquals(25, $response->json('data.pagination.total'));
        $this->assertTrue($response->json('data.pagination.has_more_pages'));

        // Test second page
        $response = $this->getJson("/api/products/{$productId}/reviews?per_page=10&page=2");

        $response->assertStatus(200);
        $this->assertCount(10, $response->json('data.reviews'));
        $this->assertEquals(2, $response->json('data.pagination.current_page'));
    }

    public function test_returns_empty_statistics_when_no_statistics_exist()
    {
        $productId = 'product_without_stats';
        
        $response = $this->getJson("/api/products/{$productId}/reviews");

        $response->assertStatus(200);
        $this->assertNull($response->json('data.statistics'));
        $this->assertEmpty($response->json('data.reviews'));
    }

    public function test_can_get_product_rating_summary()
    {
        $productId = 'product_123';
        
        // Create statistics for the product
        $statistics = $this->statisticsFactory->create([
            'product_id' => $productId,
            'rating_count' => 100,
            'average_rating' => 4.2,
            'rating_distribution' => [1 => 5, 2 => 10, 3 => 15, 4 => 30, 5 => 40],
            'percentage_distribution' => [1 => 5.0, 2 => 10.0, 3 => 15.0, 4 => 30.0, 5 => 40.0],
        ]);

        $response = $this->getJson("/api/products/{$productId}/rating");

        $response->assertStatus(200)
            ->assertJsonStructure([
                'data' => [
                    'product_id',
                    'rating_count',
                    'average_rating',
                    'rating_distribution',
                    'percentage_distribution',
                    'last_calculated_at',
                ]
            ]);

        $this->assertEquals($productId, $response->json('data.product_id'));
        $this->assertEquals(100, $response->json('data.rating_count'));
        $this->assertEquals(4.2, $response->json('data.average_rating'));
    }

    public function test_rating_summary_returns_404_when_no_statistics()
    {
        $productId = 'product_without_stats';
        
        $response = $this->getJson("/api/products/{$productId}/rating");

        $response->assertStatus(404)
            ->assertJson([
                'message' => 'No rating statistics found for this product.'
            ]);
    }

    public function test_can_get_bulk_product_ratings()
    {
        // Create statistics for multiple products
        $product1Stats = $this->statisticsFactory->create([
            'product_id' => 'product_1',
            'rating_count' => 50,
            'average_rating' => 4.5,
        ]);
        
        $product2Stats = $this->statisticsFactory->create([
            'product_id' => 'product_2',
            'rating_count' => 30,
            'average_rating' => 3.8,
        ]);

        $requestData = [
            'product_ids' => ['product_1', 'product_2', 'product_3'] // product_3 has no stats
        ];

        $response = $this->postJson('/api/products/ratings-summary', $requestData);

        $response->assertStatus(200)
            ->assertJsonStructure([
                'data' => [
                    '*' => [
                        'product_id',
                        'rating_count',
                        'average_rating',
                        'rating_distribution',
                        'percentage_distribution',
                        'last_calculated_at',
                    ]
                ]
            ]);

        // Should return statistics for products that have them
        $this->assertCount(2, $response->json('data'));
        
        $productIds = collect($response->json('data'))->pluck('product_id')->toArray();
        $this->assertContains('product_1', $productIds);
        $this->assertContains('product_2', $productIds);
        $this->assertNotContains('product_3', $productIds);
    }

    public function test_bulk_ratings_validation_fails_for_missing_product_ids()
    {
        $response = $this->postJson('/api/products/ratings-summary', []);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['product_ids']);
    }

    public function test_bulk_ratings_validation_fails_for_empty_product_ids()
    {
        $response = $this->postJson('/api/products/ratings-summary', [
            'product_ids' => []
        ]);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['product_ids']);
    }

    public function test_reviews_are_sorted_by_creation_date_desc()
    {
        $productId = 'product_123';
        
        // Create reviews with different creation dates
        $olderReview = $this->reviewFactory->create([
            'product_id' => $productId,
            'publication_status' => 'published',
            'created_at' => '2024-01-01T10:00:00Z',
        ]);
        
        $newerReview = $this->reviewFactory->create([
            'product_id' => $productId,
            'publication_status' => 'published',
            'created_at' => '2024-01-02T10:00:00Z',
        ]);

        $response = $this->getJson("/api/products/{$productId}/reviews");

        $response->assertStatus(200);
        
        $reviews = $response->json('data.reviews');
        $this->assertCount(2, $reviews);
        
        // Newer review should come first
        $this->assertEquals($newerReview->review_id, $reviews[0]['review_id']);
        $this->assertEquals($olderReview->review_id, $reviews[1]['review_id']);
    }
}
