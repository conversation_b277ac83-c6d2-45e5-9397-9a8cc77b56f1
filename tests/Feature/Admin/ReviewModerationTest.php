<?php

namespace Tests\Feature\Admin;

use App\Models\RatingAndReview;
use App\Services\CloudFrontService;
use App\Services\RatingsAndReviewsStatisticsService;
use Database\Factories\RatingAndReviewFactory;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Queue;
use Tests\TestCase;

class ReviewModerationTest extends TestCase
{
    use RefreshDatabase;

    protected RatingAndReviewFactory $reviewFactory;

    protected function setUp(): void
    {
        parent::setUp();
        $this->reviewFactory = new RatingAndReviewFactory();
        
        // Mock external services
        $this->mock(CloudFrontService::class, function ($mock) {
            $mock->shouldReceive('invalidateReviewApi')->andReturn(true);
            $mock->shouldReceive('invalidateProductReviewsApi')->andReturn(true);
        });
        
        $this->mock(RatingsAndReviewsStatisticsService::class, function ($mock) {
            $mock->shouldReceive('queueStatisticsUpdate')->andReturn(true);
        });
        
        Queue::fake();
    }

    public function test_admin_can_get_reviews_by_status()
    {
        // Create reviews with different statuses
        $pendingReview = $this->reviewFactory->create(['publication_status' => 'pending']);
        $publishedReview = $this->reviewFactory->create(['publication_status' => 'published']);
        $rejectedReview = $this->reviewFactory->create(['publication_status' => 'rejected']);

        // Test getting pending reviews
        $response = $this->getJson('/api/reviews?status=pending');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'data' => [
                    '*' => [
                        'review_id',
                        'user_id',
                        'product_id',
                        'rating',
                        'original_language',
                        'review_en',
                        'review_ar',
                        'country',
                        'created_at',
                        'media',
                        'publication_status',
                    ]
                ],
                'pagination' => [
                    'current_page',
                    'per_page',
                    'total',
                    'has_more_pages',
                ]
            ]);

        $this->assertCount(1, $response->json('data'));
        $this->assertEquals('pending', $response->json('data.0.publication_status'));
    }

    public function test_admin_can_filter_reviews_by_user()
    {
        $userId = 'user_123';
        
        // Create reviews for different users
        $userReview = $this->reviewFactory->create(['user_id' => $userId]);
        $otherUserReview = $this->reviewFactory->create(['user_id' => 'user_456']);

        $response = $this->getJson("/api/reviews?user_id={$userId}");

        $response->assertStatus(200);
        $this->assertCount(1, $response->json('data'));
        $this->assertEquals($userId, $response->json('data.0.user_id'));
    }

    public function test_admin_can_filter_reviews_by_product()
    {
        $productId = 'product_123';
        
        // Create reviews for different products
        $productReview = $this->reviewFactory->create(['product_id' => $productId]);
        $otherProductReview = $this->reviewFactory->create(['product_id' => 'product_456']);

        $response = $this->getJson("/api/reviews?product_id={$productId}");

        $response->assertStatus(200);
        $this->assertCount(1, $response->json('data'));
        $this->assertEquals($productId, $response->json('data.0.product_id'));
    }

    public function test_admin_can_update_review_publication_status()
    {
        $review = $this->reviewFactory->create(['publication_status' => 'pending']);

        $response = $this->putJson("/api/reviews/{$review->review_id}/publication", [
            'publication_status' => 'published'
        ]);

        $response->assertStatus(200)
            ->assertJson([
                'data' => [
                    'review_id' => $review->review_id,
                    'publication_status' => 'published',
                ]
            ]);

        // Verify the review was updated in the database
        $updatedReview = RatingAndReview::find($review->review_id);
        $this->assertEquals('published', $updatedReview->publication_status);
    }

    public function test_admin_can_reject_review()
    {
        $review = $this->reviewFactory->create(['publication_status' => 'pending']);

        $response = $this->putJson("/api/reviews/{$review->review_id}/publication", [
            'publication_status' => 'rejected'
        ]);

        $response->assertStatus(200);
        $this->assertEquals('rejected', $response->json('data.publication_status'));
    }

    public function test_publication_status_update_validation()
    {
        $review = $this->reviewFactory->create(['publication_status' => 'pending']);

        // Test invalid status
        $response = $this->putJson("/api/reviews/{$review->review_id}/publication", [
            'publication_status' => 'invalid_status'
        ]);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['publication_status']);

        // Test missing status
        $response = $this->putJson("/api/reviews/{$review->review_id}/publication", []);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['publication_status']);
    }

    public function test_admin_can_delete_review()
    {
        $review = $this->reviewFactory->create();

        $response = $this->deleteJson("/api/reviews/{$review->review_id}");

        $response->assertStatus(200)
            ->assertJson([
                'message' => 'Review deleted successfully.'
            ]);

        // Verify the review was deleted
        $this->assertNull(RatingAndReview::find($review->review_id));
    }

    public function test_delete_nonexistent_review_returns_404()
    {
        $response = $this->deleteJson('/api/reviews/nonexistent-review-id');

        $response->assertStatus(404)
            ->assertJson([
                'message' => 'Review not found.'
            ]);
    }

    public function test_admin_can_check_for_pending_reviews()
    {
        // Create some pending reviews
        $this->reviewFactory->create(['publication_status' => 'pending']);
        $this->reviewFactory->create(['publication_status' => 'pending']);
        $this->reviewFactory->create(['publication_status' => 'published']);

        $response = $this->getJson('/api/reviews/pending-check');

        $response->assertStatus(200)
            ->assertJson([
                'has_pending_reviews' => true
            ]);
    }

    public function test_pending_check_returns_false_when_no_pending_reviews()
    {
        // Create only published/rejected reviews
        $this->reviewFactory->create(['publication_status' => 'published']);
        $this->reviewFactory->create(['publication_status' => 'rejected']);

        $response = $this->getJson('/api/reviews/pending-check');

        $response->assertStatus(200)
            ->assertJson([
                'has_pending_reviews' => false
            ]);
    }

    public function test_admin_can_get_review_counts_by_status()
    {
        // Create reviews with different statuses
        $this->reviewFactory->create(['publication_status' => 'pending']);
        $this->reviewFactory->create(['publication_status' => 'pending']);
        $this->reviewFactory->create(['publication_status' => 'published']);
        $this->reviewFactory->create(['publication_status' => 'published']);
        $this->reviewFactory->create(['publication_status' => 'published']);
        $this->reviewFactory->create(['publication_status' => 'rejected']);

        $response = $this->getJson('/api/reviews/counts-by-status-bk');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'data' => [
                    'pending',
                    'published',
                    'rejected',
                    'total',
                ]
            ]);

        $data = $response->json('data');
        $this->assertEquals(2, $data['pending']);
        $this->assertEquals(3, $data['published']);
        $this->assertEquals(1, $data['rejected']);
        $this->assertEquals(6, $data['total']);
    }

    public function test_review_counts_returns_zero_when_no_reviews()
    {
        $response = $this->getJson('/api/reviews/counts-by-status-bk');

        $response->assertStatus(200);
        
        $data = $response->json('data');
        $this->assertEquals(0, $data['pending']);
        $this->assertEquals(0, $data['published']);
        $this->assertEquals(0, $data['rejected']);
        $this->assertEquals(0, $data['total']);
    }

    public function test_admin_reviews_pagination()
    {
        // Create multiple reviews
        for ($i = 0; $i < 25; $i++) {
            $this->reviewFactory->create(['publication_status' => 'pending']);
        }

        $response = $this->getJson('/api/reviews?status=pending&per_page=10');

        $response->assertStatus(200);
        $this->assertCount(10, $response->json('data'));
        $this->assertEquals(1, $response->json('pagination.current_page'));
        $this->assertEquals(10, $response->json('pagination.per_page'));
        $this->assertEquals(25, $response->json('pagination.total'));
        $this->assertTrue($response->json('pagination.has_more_pages'));
    }

    public function test_admin_can_combine_multiple_filters()
    {
        $userId = 'user_123';
        $productId = 'product_456';
        
        // Create reviews with different combinations
        $targetReview = $this->reviewFactory->create([
            'user_id' => $userId,
            'product_id' => $productId,
            'publication_status' => 'pending'
        ]);
        
        $this->reviewFactory->create([
            'user_id' => $userId,
            'product_id' => 'other_product',
            'publication_status' => 'pending'
        ]);
        
        $this->reviewFactory->create([
            'user_id' => 'other_user',
            'product_id' => $productId,
            'publication_status' => 'pending'
        ]);

        $response = $this->getJson("/api/reviews?user_id={$userId}&product_id={$productId}&status=pending");

        $response->assertStatus(200);
        $this->assertCount(1, $response->json('data'));
        $this->assertEquals($targetReview->review_id, $response->json('data.0.review_id'));
    }

    public function test_cloudfront_invalidation_triggered_on_status_update()
    {
        $review = $this->reviewFactory->create(['publication_status' => 'pending']);

        $cloudFrontService = $this->mock(CloudFrontService::class);
        $cloudFrontService->shouldReceive('invalidateReviewApi')
            ->once()
            ->with($review->review_id);
        $cloudFrontService->shouldReceive('invalidateProductReviewsApi')
            ->once()
            ->with($review->product_id);

        $response = $this->putJson("/api/reviews/{$review->review_id}/publication", [
            'publication_status' => 'published'
        ]);

        $response->assertStatus(200);
    }

    public function test_statistics_update_queued_on_status_change()
    {
        $review = $this->reviewFactory->create(['publication_status' => 'pending']);

        $statisticsService = $this->mock(RatingsAndReviewsStatisticsService::class);
        $statisticsService->shouldReceive('queueStatisticsUpdate')
            ->once()
            ->with($review->product_id);

        $response = $this->putJson("/api/reviews/{$review->review_id}/publication", [
            'publication_status' => 'published'
        ]);

        $response->assertStatus(200);
    }

    public function test_update_nonexistent_review_returns_404()
    {
        $response = $this->putJson('/api/reviews/nonexistent-review-id/publication', [
            'publication_status' => 'published'
        ]);

        $response->assertStatus(404)
            ->assertJson([
                'message' => 'Review not found.'
            ]);
    }
}
