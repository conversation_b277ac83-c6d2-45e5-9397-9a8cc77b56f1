<?php

namespace Database\Factories;

use App\Models\RatingsAndReviewStatistics;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\RatingsAndReviewStatistics>
 */
class RatingsAndReviewStatisticsFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = RatingsAndReviewStatistics::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        // Generate realistic rating distribution
        $totalReviews = $this->faker->numberBetween(10, 1000);
        $distribution = $this->generateRealisticDistribution($totalReviews);
        
        return [
            'product_id' => 'product_' . $this->faker->uuid(),
            'rating_count' => $totalReviews,
            'average_rating' => $this->calculateAverageRating($distribution),
            'rating_distribution' => $distribution,
            'percentage_distribution' => $this->calculatePercentageDistribution($distribution, $totalReviews),
            'last_calculated_at' => now()->toIso8601String(),
        ];
    }

    /**
     * Generate a realistic rating distribution.
     * Most products tend to have higher ratings with some variation.
     */
    private function generateRealisticDistribution(int $totalReviews): array
    {
        // Create a distribution that favors higher ratings (more realistic)
        $weights = [1 => 5, 2 => 8, 3 => 15, 4 => 35, 5 => 37]; // Percentages
        
        $distribution = [1 => 0, 2 => 0, 3 => 0, 4 => 0, 5 => 0];
        $remaining = $totalReviews;
        
        // Distribute reviews based on weights
        for ($rating = 1; $rating <= 4; $rating++) {
            $count = (int) round(($weights[$rating] / 100) * $totalReviews);
            $count = min($count, $remaining); // Don't exceed remaining
            $distribution[$rating] = $count;
            $remaining -= $count;
        }
        
        // Assign remaining to 5-star rating
        $distribution[5] = $remaining;
        
        return $distribution;
    }

    /**
     * Calculate average rating from distribution.
     */
    private function calculateAverageRating(array $distribution): float
    {
        $totalRating = 0;
        $totalCount = 0;
        
        foreach ($distribution as $rating => $count) {
            $totalRating += $rating * $count;
            $totalCount += $count;
        }
        
        return $totalCount > 0 ? round($totalRating / $totalCount, 2) : 0.0;
    }

    /**
     * Calculate percentage distribution from count distribution.
     */
    private function calculatePercentageDistribution(array $distribution, int $totalCount): array
    {
        $percentages = [];
        
        foreach ($distribution as $rating => $count) {
            $percentages[$rating] = $totalCount > 0 ? round(($count / $totalCount) * 100, 2) : 0.0;
        }
        
        return $percentages;
    }

    /**
     * Create statistics for a specific product.
     */
    public function forProduct(string $productId): static
    {
        return $this->state(fn (array $attributes) => [
            'product_id' => $productId,
        ]);
    }

    /**
     * Create statistics with a specific total review count.
     */
    public function withReviewCount(int $count): static
    {
        return $this->state(function (array $attributes) use ($count) {
            $distribution = $this->generateRealisticDistribution($count);
            
            return [
                'rating_count' => $count,
                'rating_distribution' => $distribution,
                'average_rating' => $this->calculateAverageRating($distribution),
                'percentage_distribution' => $this->calculatePercentageDistribution($distribution, $count),
            ];
        });
    }

    /**
     * Create statistics with a specific average rating.
     */
    public function withAverageRating(float $averageRating): static
    {
        return $this->state(function (array $attributes) use ($averageRating) {
            $totalReviews = $this->faker->numberBetween(50, 500);
            $distribution = $this->generateDistributionForAverage($averageRating, $totalReviews);
            
            return [
                'rating_count' => $totalReviews,
                'average_rating' => $averageRating,
                'rating_distribution' => $distribution,
                'percentage_distribution' => $this->calculatePercentageDistribution($distribution, $totalReviews),
            ];
        });
    }

    /**
     * Generate distribution that results in a specific average rating.
     */
    private function generateDistributionForAverage(float $targetAverage, int $totalReviews): array
    {
        // Simple algorithm to generate distribution for target average
        $distribution = [1 => 0, 2 => 0, 3 => 0, 4 => 0, 5 => 0];
        
        // Start with all reviews at the target rating (rounded)
        $baseRating = (int) round($targetAverage);
        $distribution[$baseRating] = $totalReviews;
        
        // Adjust to get closer to target average
        $currentAverage = $baseRating;
        $iterations = 0;
        
        while (abs($currentAverage - $targetAverage) > 0.05 && $iterations < 100) {
            if ($currentAverage > $targetAverage) {
                // Move some reviews to lower ratings
                for ($rating = 5; $rating >= 2; $rating--) {
                    if ($distribution[$rating] > 0) {
                        $distribution[$rating]--;
                        $distribution[$rating - 1]++;
                        break;
                    }
                }
            } else {
                // Move some reviews to higher ratings
                for ($rating = 1; $rating <= 4; $rating++) {
                    if ($distribution[$rating] > 0) {
                        $distribution[$rating]--;
                        $distribution[$rating + 1]++;
                        break;
                    }
                }
            }
            
            $currentAverage = $this->calculateAverageRating($distribution);
            $iterations++;
        }
        
        return $distribution;
    }

    /**
     * Create statistics with no reviews.
     */
    public function empty(): static
    {
        return $this->state(fn (array $attributes) => [
            'rating_count' => 0,
            'average_rating' => 0.0,
            'rating_distribution' => [1 => 0, 2 => 0, 3 => 0, 4 => 0, 5 => 0],
            'percentage_distribution' => [1 => 0.0, 2 => 0.0, 3 => 0.0, 4 => 0.0, 5 => 0.0],
        ]);
    }

    /**
     * Create statistics with high ratings (4-5 stars mostly).
     */
    public function highRated(): static
    {
        return $this->state(function (array $attributes) {
            $totalReviews = $this->faker->numberBetween(50, 300);
            $distribution = [
                1 => (int) round($totalReviews * 0.02), // 2%
                2 => (int) round($totalReviews * 0.03), // 3%
                3 => (int) round($totalReviews * 0.10), // 10%
                4 => (int) round($totalReviews * 0.35), // 35%
                5 => 0, // Will be calculated
            ];
            
            // Assign remaining to 5-star
            $distribution[5] = $totalReviews - array_sum(array_slice($distribution, 0, 4, true));
            
            return [
                'rating_count' => $totalReviews,
                'rating_distribution' => $distribution,
                'average_rating' => $this->calculateAverageRating($distribution),
                'percentage_distribution' => $this->calculatePercentageDistribution($distribution, $totalReviews),
            ];
        });
    }

    /**
     * Create statistics with low ratings (1-2 stars mostly).
     */
    public function lowRated(): static
    {
        return $this->state(function (array $attributes) {
            $totalReviews = $this->faker->numberBetween(20, 100);
            $distribution = [
                1 => (int) round($totalReviews * 0.40), // 40%
                2 => (int) round($totalReviews * 0.30), // 30%
                3 => (int) round($totalReviews * 0.20), // 20%
                4 => (int) round($totalReviews * 0.07), // 7%
                5 => 0, // Will be calculated
            ];
            
            // Assign remaining to 5-star
            $distribution[5] = $totalReviews - array_sum(array_slice($distribution, 0, 4, true));
            
            return [
                'rating_count' => $totalReviews,
                'rating_distribution' => $distribution,
                'average_rating' => $this->calculateAverageRating($distribution),
                'percentage_distribution' => $this->calculatePercentageDistribution($distribution, $totalReviews),
            ];
        });
    }
}
