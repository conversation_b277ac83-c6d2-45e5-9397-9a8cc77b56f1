# Testing Guide for Ratings and Reviews Service

This document provides comprehensive information about testing the Ratings and Reviews microservice.

## Test Structure

The test suite is organized into three main categories following Laravel best practices:

```
tests/
├── Unit/                    # Unit tests for individual components
│   ├── Models/             # Model behavior and validation tests
│   ├── Services/           # Business logic testing
│   └── Jobs/              # Background job testing
├── Feature/                # Feature tests for API endpoints
│   ├── API/               # General API functionality
│   ├── Admin/             # Admin-specific features
│   └── Customer/          # Customer-facing features
└── Integration/           # Integration tests
    ├── DynamoDB/          # Database integration testing
    ├── Translation/       # External service testing
    └── Storage/           # File storage testing
```

## Test Categories

### Unit Tests

Unit tests focus on testing individual components in isolation:

- **Models**: Test model attributes, casts, scopes, and validation
- **Services**: Test business logic without external dependencies
- **Jobs**: Test background job execution and error handling

### Feature Tests

Feature tests verify complete user workflows:

- **Customer APIs**: Review creation, translation, product reviews
- **Admin APIs**: Review moderation, status updates, filtering
- **API Validation**: Request validation and error responses

### Integration Tests

Integration tests verify component interactions:

- **DynamoDB**: Database operations, indexes, and queries
- **External Services**: Google Translate, AWS S3, CloudFront
- **File Storage**: Media upload and retrieval

## Running Tests

### Prerequisites

1. **Docker Environment**: Ensure Docker and Docker Compose are running
2. **DynamoDB Local**: Start the local DynamoDB instance
3. **Dependencies**: Install PHP dependencies

```bash
# Start the development environment
docker-compose up -d

# Install dependencies
composer install
```

### Running All Tests

```bash
# Run all tests
./vendor/bin/phpunit

# Run tests with coverage (if xdebug is enabled)
./vendor/bin/phpunit --coverage-html coverage
```

### Running Specific Test Categories

```bash
# Run only unit tests
./vendor/bin/phpunit tests/Unit

# Run only feature tests
./vendor/bin/phpunit tests/Feature

# Run only integration tests
./vendor/bin/phpunit tests/Integration
```

### Running Specific Test Files

```bash
# Run a specific test file
./vendor/bin/phpunit tests/Unit/Models/RatingAndReviewTest.php

# Run a specific test method
./vendor/bin/phpunit --filter test_can_create_review_with_valid_data tests/Feature/Customer/ReviewCreationTest.php
```

## Test Configuration

### Environment Variables

The test environment uses specific configuration values defined in `phpunit.xml`:

```xml
<!-- DynamoDB Testing Configuration -->
<env name="DYNAMODB_CONNECTION" value="testing"/>
<env name="DYNAMODB_LOCAL_ENDPOINT" value="http://localhost:8000"/>
<env name="AWS_ACCESS_KEY_ID" value="testing"/>
<env name="AWS_SECRET_ACCESS_KEY" value="testing"/>

<!-- External Services Testing -->
<env name="GOOGLE_TRANSLATE_API_KEY" value="testing-key"/>
<env name="CLOUDFRONT_DISTRIBUTION_ID" value="testing-distribution"/>
<env name="FILESYSTEM_DISK" value="local"/>
```

### Test Database

Tests use a local DynamoDB instance running in Docker. The test environment:

- Uses separate tables with `_test` suffix
- Automatically creates and destroys test data
- Mocks external API calls to avoid rate limits

## Test Factories

The test suite includes comprehensive factories for generating test data:

### RatingAndReviewFactory

```php
// Create a basic review
$review = $factory->create();

// Create a published review
$review = $factory->create(['publication_status' => 'published']);

// Create a review with media
$review = $factory->makeWithMultipleMedia(3);

// Create a review for a specific product
$review = $factory->create(['product_id' => 'product_123']);
```

### RatingsAndReviewStatisticsFactory

```php
// Create basic statistics
$stats = $factory->create();

// Create statistics for a specific product
$stats = $factory->forProduct('product_123')->create();

// Create high-rated product statistics
$stats = $factory->highRated()->create();

// Create statistics with specific review count
$stats = $factory->withReviewCount(100)->create();
```

## Mocking External Services

Tests mock external services to ensure reliability and speed:

### Google Translate API

```php
Http::fake([
    'translation.googleapis.com/*' => Http::response([
        'data' => [
            'translations' => [
                ['translatedText' => 'Translated text']
            ]
        ]
    ], 200)
]);
```

### CloudFront Service

```php
$this->mock(CloudFrontService::class, function ($mock) {
    $mock->shouldReceive('invalidateProductReviewsApi')->andReturn(true);
    $mock->shouldReceive('invalidateReviewApi')->andReturn(true);
});
```

### Queue Jobs

```php
Queue::fake();

// Assert that a job was dispatched
Queue::assertPushed(UpdateProductStatisticsJob::class);
```

## Test Data Management

### Database Refresh

Feature and integration tests use `RefreshDatabase` trait to ensure clean state:

```php
use Illuminate\Foundation\Testing\RefreshDatabase;

class ReviewCreationTest extends TestCase
{
    use RefreshDatabase;
    
    // Tests automatically start with clean database
}
```

### Test Isolation

Each test method runs in isolation:

- Database transactions are rolled back after each test
- Mocks are reset between tests
- External service calls are faked

## Common Test Patterns

### API Testing

```php
public function test_can_create_review()
{
    $data = [
        'user_id' => 'user_123',
        'product_id' => 'product_456',
        'rating' => 5,
        'review_en' => 'Great product!',
        'country' => 'AE',
    ];

    $response = $this->postJson('/api/reviews', $data);

    $response->assertStatus(201)
        ->assertJsonStructure(['data' => ['review_id', 'rating']]);
}
```

### Validation Testing

```php
public function test_validation_fails_for_invalid_rating()
{
    $data = ['rating' => 6]; // Invalid rating

    $response = $this->postJson('/api/reviews', $data);

    $response->assertStatus(422)
        ->assertJsonValidationErrors(['rating']);
}
```

### Service Testing

```php
public function test_translation_service_translates_review()
{
    $review = $this->createTestReview(['review_en' => 'Hello']);
    
    $result = $this->translationService->translateReview($review);
    
    $this->assertTrue($result);
    $this->assertNotNull($review->review_ar);
}
```

## Debugging Tests

### Verbose Output

```bash
# Run tests with detailed output
./vendor/bin/phpunit --testdox

# Run with debug information
./vendor/bin/phpunit --debug
```

### Test Specific Issues

```bash
# Run a single test with maximum verbosity
./vendor/bin/phpunit --testdox --stop-on-failure tests/Unit/Models/RatingAndReviewTest.php
```

### Database Debugging

For DynamoDB-related test issues:

1. Check DynamoDB container status: `docker-compose ps dynamodb`
2. View DynamoDB logs: `docker-compose logs dynamodb`
3. Access DynamoDB Admin: http://localhost:8001

## Continuous Integration

The test suite is designed to run in CI environments:

### GitHub Actions Example

```yaml
- name: Run Tests
  run: |
    docker-compose up -d dynamodb redis
    sleep 10  # Wait for services to start
    ./vendor/bin/phpunit
```

### Test Coverage

Generate coverage reports:

```bash
# HTML coverage report
./vendor/bin/phpunit --coverage-html coverage

# Text coverage summary
./vendor/bin/phpunit --coverage-text
```

## Best Practices

1. **Test Naming**: Use descriptive test method names that explain the scenario
2. **Arrange-Act-Assert**: Structure tests with clear setup, execution, and verification
3. **Mock External Dependencies**: Always mock external APIs and services
4. **Test Edge Cases**: Include tests for error conditions and boundary values
5. **Keep Tests Fast**: Use mocks and fakes to avoid slow operations
6. **Test Data**: Use factories for consistent test data generation

## Troubleshooting

### Common Issues

1. **DynamoDB Connection Errors**: Ensure DynamoDB container is running
2. **Memory Issues**: Increase PHP memory limit for large test suites
3. **Timeout Issues**: Check external service mocks are properly configured
4. **Permission Issues**: Ensure proper file permissions for storage tests

### Getting Help

- Check the test output for specific error messages
- Review the test configuration in `phpunit.xml`
- Verify Docker services are running with `docker-compose ps`
- Check application logs for detailed error information

This testing guide ensures comprehensive coverage of the Ratings and Reviews service functionality while maintaining fast and reliable test execution.
